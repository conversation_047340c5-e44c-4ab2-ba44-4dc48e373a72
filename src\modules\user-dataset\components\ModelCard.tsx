import React from 'react';
import { Card, Icon } from '@/shared/components/common';
import { useTranslation } from 'react-i18next';
import {
  UserModeBaseResponseDto,
  UserModelsByKeyResponseDto,
} from '../user-mode-base/types/user-mode-base.types';
import { UserModelFineTuneResponseDto } from '../user-mode-fine-tune/types/user-model-fine-tune.types';

// Union type cho tất cả các loại model
type ModelType =
  | UserModeBaseResponseDto
  | UserModelsByKeyResponseDto
  | UserModelFineTuneResponseDto;

interface ModelCardProps {
  model: ModelType;
  onClick?: () => void;
  className?: string;
}

const ModelCard: React.FC<ModelCardProps> = ({ model, onClick, className = '' }) => {
  const { t } = useTranslation();

  // Helper function để lấy tên model
  const getModelName = (model: ModelType): string => {
    if ('name' in model && model.name) {
      return model.name;
    }
    if ('modelId' in model && model.modelId) {
      return model.modelId;
    }
    if ('id' in model) {
      return model.id;
    }
    return 'Unknown Model';
  };

  // Helper function để lấy provider
  const getModelProvider = (model: ModelType): string => {
    if ('provider' in model && model.provider) {
      return String(model.provider);
    }
    if ('baseModelName' in model && model.baseModelName) {
      // Đối với fine-tune models, hiển thị base model name
      return String(model.baseModelName);
    }
    return 'Unknown Provider';
  };

  const getProviderIcon = (provider: string): JSX.Element => {
    const providerLower = provider.toLowerCase();
    if (providerLower.includes('openai')) return <Icon name="openai" />;
    if (providerLower.includes('anthropic')) return <span>🧠</span>;
    if (providerLower.includes('google')) return <span>🔍</span>;
    if (providerLower.includes('gpt')) return <span>🤖</span>;
    return <span>⚡</span>;
  };

  const modelName = getModelName(model);
  const modelProvider = getModelProvider(model);
  const providerIcon = getProviderIcon(modelProvider);

  return (
    <Card
      className={`h-full overflow-hidden shadow-md hover:shadow-lg transition-shadow duration-300 cursor-pointer ${className}`}
      variant="elevated"
      onClick={onClick}
    >
      <div className="p-4">
        {/* Header: Icon + Tên Model */}
        <div className="flex items-center gap-3 mb-3">
          {/* Provider Icon */}
          <div className="w-10 h-10 flex-shrink-0 bg-gray-100 dark:bg-gray-700 rounded-lg flex items-center justify-center">
            <span className="text-lg">{providerIcon}</span>
          </div>

          {/* Tên Model */}
          <div className="min-w-0 flex-1">
            <h3 className="font-semibold text-base text-gray-900 dark:text-white truncate">
              {modelName}
            </h3>
          </div>
        </div>

        {/* Provider Info */}
        <div className="text-sm text-gray-600 dark:text-gray-400">
          <span className="font-medium">{t('Provider')}: </span>
          <span>{modelProvider}</span>
        </div>
      </div>
    </Card>
  );
};

export default ModelCard;
