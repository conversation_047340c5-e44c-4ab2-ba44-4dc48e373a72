import { apiClient } from '@/shared/api/axios';
import {
  UserModeBaseResponseDto,
  UserModeBaseDetailResponseDto,
  UserModeBaseQueryDto,
  CompareModesDto,
  CompareModesResponseDto,
  RateModeDto,
  RateModeResponseDto,
  PaginatedResult,
  UserKeyLLMResponseDto,
  UserKeyLLMQueryDto,
  UserModelsByKeyResponseDto,
} from '../types/user-mode-base.types';
import { UserModelFineTuneResponseDto } from '../../user-mode-fine-tune/types/user-model-fine-tune.types';

const API_BASE_URL = '/user/mode-base';

/**
 * Lấy danh sách mode base có phân trang
 * @param queryDto Tham số truy vấn
 * @returns Danh sách mode base với phân trang
 */
export const getUserModeBaseList = async (
  queryDto?: UserModeBaseQueryDto
): Promise<PaginatedResult<UserModeBaseResponseDto>> => {
  const queryParams = new URLSearchParams();

  if (queryDto?.page) queryParams.append('page', queryDto.page.toString());
  if (queryDto?.limit) queryParams.append('limit', queryDto.limit.toString());
  if (queryDto?.search) queryParams.append('search', queryDto.search);
  if (queryDto?.provider) queryParams.append('provider', queryDto.provider);
  if (queryDto?.type) queryParams.append('type', queryDto.type);
  if (queryDto?.status) queryParams.append('status', queryDto.status);
  if (queryDto?.supportsFineTuning !== undefined) {
    queryParams.append('supportsFineTuning', queryDto.supportsFineTuning.toString());
  }
  if (queryDto?.supportsFunctionCalling !== undefined) {
    queryParams.append('supportsFunctionCalling', queryDto.supportsFunctionCalling.toString());
  }
  if (queryDto?.supportsVision !== undefined) {
    queryParams.append('supportsVision', queryDto.supportsVision.toString());
  }
  if (queryDto?.sortBy) queryParams.append('sortBy', queryDto.sortBy);
  if (queryDto?.sortDirection) queryParams.append('sortDirection', queryDto.sortDirection);

  const queryString = queryParams.toString();
  const url = queryString ? `${API_BASE_URL}?${queryString}` : API_BASE_URL;

  const response = await apiClient.get<PaginatedResult<UserModeBaseResponseDto>>(url);
  return response.result;
};

/**
 * Lấy chi tiết mode base
 * @param id ID của mode base
 * @returns Thông tin chi tiết mode base
 */
export const getUserModeBaseDetail = async (id: string): Promise<UserModeBaseDetailResponseDto> => {
  const response = await apiClient.get<UserModeBaseDetailResponseDto>(`${API_BASE_URL}/${id}`);
  return response.result;
};

/**
 * So sánh nhiều modes
 * @param data Dữ liệu so sánh
 * @returns Kết quả so sánh
 */
export const compareModes = async (data: CompareModesDto): Promise<CompareModesResponseDto> => {
  const response = await apiClient.post<CompareModesResponseDto>(`${API_BASE_URL}/compare`, data);
  return response.result;
};

/**
 * Đánh giá mode
 * @param id ID của mode
 * @param data Dữ liệu đánh giá
 * @returns Kết quả đánh giá
 */
export const rateMode = async (id: string, data: RateModeDto): Promise<RateModeResponseDto> => {
  const response = await apiClient.post<RateModeResponseDto>(`${API_BASE_URL}/${id}/rate`, data);
  return response.result;
};

/**
 * Lấy danh sách modes phổ biến
 * @param limit Số lượng modes
 * @returns Danh sách modes phổ biến
 */
export const getPopularModes = async (limit: number = 10): Promise<UserModeBaseResponseDto[]> => {
  const response = await apiClient.get<UserModeBaseResponseDto[]>(
    `${API_BASE_URL}/popular?limit=${limit}`
  );
  return response.result;
};

/**
 * Lấy danh sách modes được khuyến nghị
 * @param useCase Use case để khuyến nghị
 * @param limit Số lượng modes
 * @returns Danh sách modes được khuyến nghị
 */
export const getRecommendedModes = async (
  useCase: string,
  limit: number = 5
): Promise<UserModeBaseResponseDto[]> => {
  const response = await apiClient.get<UserModeBaseResponseDto[]>(
    `${API_BASE_URL}/recommended?useCase=${encodeURIComponent(useCase)}&limit=${limit}`
  );
  return response.result;
};

/**
 * Tìm kiếm modes theo từ khóa
 * @param query Từ khóa tìm kiếm
 * @param limit Số lượng kết quả
 * @returns Danh sách modes tìm được
 */
export const searchModes = async (
  query: string,
  limit: number = 20
): Promise<UserModeBaseResponseDto[]> => {
  const response = await apiClient.get<UserModeBaseResponseDto[]>(
    `${API_BASE_URL}/search?q=${encodeURIComponent(query)}&limit=${limit}`
  );
  return response.result;
};

/**
 * Lấy thống kê sử dụng mode
 * @param id ID của mode
 * @returns Thống kê sử dụng
 */
export const getModeUsageStats = async (
  id: string
): Promise<{
  totalUsage: number;
  monthlyUsage: number;
  weeklyUsage: number;
  dailyUsage: number;
  usageHistory: Array<{
    date: string;
    count: number;
  }>;
}> => {
  const response = await apiClient.get<{
    totalUsage: number;
    monthlyUsage: number;
    weeklyUsage: number;
    dailyUsage: number;
    usageHistory: Array<{
      date: string;
      count: number;
    }>;
  }>(`${API_BASE_URL}/${id}/usage-stats`);
  return response.result;
};

/**
 * Lấy đánh giá của mode
 * @param id ID của mode
 * @param page Số trang
 * @param limit Số lượng đánh giá per trang
 * @returns Danh sách đánh giá
 */
export const getModeRatings = async (
  id: string,
  page: number = 1,
  limit: number = 10
): Promise<
  PaginatedResult<{
    id: string;
    rating: number;
    comment: string | null;
    useCase: string | null;
    createdAt: number;
    user: {
      id: string;
      name: string;
      avatar?: string;
    };
  }>
> => {
  const response = await apiClient.get<
    PaginatedResult<{
      id: string;
      rating: number;
      comment: string | null;
      useCase: string | null;
      createdAt: number;
      user: {
        id: string;
        name: string;
        avatar?: string;
      };
    }>
  >(`${API_BASE_URL}/${id}/ratings?page=${page}&limit=${limit}`);
  return response.result;
};

/**
 * Lấy danh sách providers có sẵn
 * @returns Danh sách providers
 */
export const getAvailableProviders = async (): Promise<
  Array<{
    provider: string;
    name: string;
    description: string;
    logoUrl?: string;
    websiteUrl?: string;
    supportedTypes: string[];
  }>
> => {
  const response = await apiClient.get<
    Array<{
      provider: string;
      name: string;
      description: string;
      logoUrl?: string;
      websiteUrl?: string;
      supportedTypes: string[];
    }>
  >(`${API_BASE_URL}/providers`);
  return response.result;
};

/**
 * Validate mode name
 * @param name Tên mode cần validate
 * @returns Kết quả validation
 */
export const validateModeName = (name: string): { isValid: boolean; errors: string[] } => {
  const errors: string[] = [];

  if (!name || name.trim().length === 0) {
    errors.push('Tên mode không được để trống');
  }

  if (name.length > 100) {
    errors.push('Tên mode không được vượt quá 100 ký tự');
  }

  if (name.length < 2) {
    errors.push('Tên mode phải có ít nhất 2 ký tự');
  }

  return {
    isValid: errors.length === 0,
    errors,
  };
};

/**
 * Format cost for display
 * @param cost Chi phí
 * @param currency Đơn vị tiền tệ
 * @returns Chuỗi hiển thị chi phí
 */
export const formatCost = (cost: number, currency: string = 'USD'): string => {
  if (cost === 0) return 'Miễn phí';

  if (cost < 0.001) {
    return `< $0.001`;
  }

  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: currency,
    minimumFractionDigits: 3,
    maximumFractionDigits: 6,
  }).format(cost);
};

/**
 * Format rating for display
 * @param rating Điểm đánh giá
 * @param totalRatings Tổng số đánh giá
 * @returns Chuỗi hiển thị rating
 */
export const formatRating = (rating: number, totalRatings: number): string => {
  if (totalRatings === 0) return 'Chưa có đánh giá';

  const stars = '★'.repeat(Math.floor(rating)) + '☆'.repeat(5 - Math.floor(rating));
  return `${stars} ${rating.toFixed(1)} (${totalRatings} đánh giá)`;
};




/**
 * Lấy danh sách user key LLM có phân trang
 * @param queryDto Tham số truy vấn
 * @returns Danh sách user key LLM với phân trang
 */
export const getUserKeyLLMList = async (
  queryDto?: UserKeyLLMQueryDto
): Promise<PaginatedResult<UserKeyLLMResponseDto>> => {
  const queryParams = new URLSearchParams();

  if (queryDto?.page) queryParams.append('page', queryDto.page.toString());
  if (queryDto?.limit) queryParams.append('limit', queryDto.limit.toString());
  if (queryDto?.search) queryParams.append('search', queryDto.search);
  if (queryDto?.provider) queryParams.append('provider', queryDto.provider);
  if (queryDto?.status) queryParams.append('status', queryDto.status);
  if (queryDto?.sortBy) queryParams.append('sortBy', queryDto.sortBy);
  if (queryDto?.sortDirection) queryParams.append('sortDirection', queryDto.sortDirection);

  const queryString = queryParams.toString();
  const url = queryString ? `/key-llm?${queryString}` : '/key-llm';

  const response = await apiClient.get<PaginatedResult<UserKeyLLMResponseDto>>(url);
  return response.result;
};

/**
 * Lấy danh sách models theo user keys
 * @param keyllmId ID của key LLM
 * @returns Danh sách models theo user key
 */
export const getUserModelsByKey = async (
  keyllmId: string
): Promise<PaginatedResult<UserModelsByKeyResponseDto>> => {
  const url = `/models/user-models-by-keys/${keyllmId}`;

  const response = await apiClient.get<{
    items: UserModelsByKeyResponseDto[];
    meta: {
      totalItems: number;
      itemCount: number;
      itemsPerPage: number;
      totalPages: number;
      currentPage: number;
      hasItems: boolean;
    };
  }>(url);

  return {
    items: response.result.items,
    meta: {
      totalItems: response.result.meta.totalItems,
      itemCount: response.result.meta.itemCount,
      itemsPerPage: response.result.meta.itemsPerPage,
      totalPages: response.result.meta.totalPages,
      currentPage: response.result.meta.currentPage
    }
  };
};

/**
 * Lấy danh sách active system models
 * @param providers Mảng providers để lọc (mặc định là ['OPENAI'])
 * @returns Danh sách system models đang hoạt động
 */
export const getActiveSystemModels = async (
  providers: string[] = ['OPENAI']
): Promise<PaginatedResult<UserModeBaseResponseDto>> => {
  const queryParams = new URLSearchParams();

  // Thêm providers vào query params - luôn có ít nhất OPENAI
  providers.forEach(provider => {
    queryParams.append('provider', provider);
  });

  const queryString = queryParams.toString();
  const url = `/models/system-models?${queryString}`;

  const response = await apiClient.get<{
    items:UserModeBaseResponseDto[];
    meta: {
      totalItems: number;
      itemCount: number;
      itemsPerPage: number;
      totalPages: number;
      currentPage: number;
    };

  }>(url)
  
  return {
    items: response.result.items,
    meta: {
      totalItems: response.result.meta.totalItems,
      itemCount: response.result.meta.itemCount,
      itemsPerPage: response.result.meta.itemsPerPage,
      totalPages: response.result.meta.totalPages,
      currentPage: response.result.meta.currentPage
    }
  };
};

export const getUserFineTuneModels = async (): Promise<PaginatedResult<UserModelFineTuneResponseDto>> => {
  const response = await apiClient.get<{
    items: UserModelFineTuneResponseDto[];
    meta: {
      totalItems: number;
      itemCount: number;
      itemsPerPage: number;
      totalPages: number;
      currentPage: number;
      hasItems?: boolean;
    };
  }>('/models/fine-tune-datasets');

  return {
    items: response.result.items,
    meta: {
      totalItems: response.result.meta.totalItems,
      itemCount: response.result.meta.itemCount,
      itemsPerPage: response.result.meta.itemsPerPage,
      totalPages: response.result.meta.totalPages,
      currentPage: response.result.meta.currentPage
    }
  };
};
