import { useCallback } from 'react';
import { useCorsAwareFileUpload } from '@/shared/hooks/common/useCorsAwareFileUpload';
import { useCreateKnowledgeFiles, KNOWLEDGE_FILE_QUERY_KEYS } from './useKnowledgeFile';
import { FileTypeEnum } from '@/shared/enums/file-type.enum';
import { useQueryClient } from '@tanstack/react-query';
import { CreateKnowledgeFileDto } from '../types';

/**
 * Hook để upload file tri thức với TaskQueue cho admin
 */
export const useKnowledgeFileUpload = () => {
  // Hook để tạo file tri thức
  const { mutateAsync: createFiles } = useCreateKnowledgeFiles();

  // Query client để cập nhật cache
  const queryClient = useQueryClient();

  // Hook để upload file với TaskQueue và xử lý lỗi CORS
  const fileUploadWithQueue = useCorsAwareFileUpload({
    defaultTaskTitle: 'Upload file tri thức',
    autoAddToQueue: true,
  });

  /**
   * Upload file tri thức với TaskQueue
   */
  const uploadKnowledgeFile = useCallback(
    async (files: File[]) => {
      try {
        // Chuẩn bị dữ liệu cho API tạo file
        const filesData: CreateKnowledgeFileDto[] = files.map(file => ({
          name: file.name,
          mime: file.type || getMimeTypeFromFileName(file.name),
          storage: file.size,
        }));

        // Gọi API để tạo file và lấy URL tạm thời
        const response = await createFiles({
          files: filesData,
        });

        // Lấy dữ liệu từ response - backend trả về mảng URL upload trong result
        const uploadUrls = response.result || [];

        // Nếu không có URLs, throw error
        if (!uploadUrls || !Array.isArray(uploadUrls) || uploadUrls.length === 0) {
          throw new Error('Không nhận được thông tin upload từ server');
        }

        // Upload từng file lên URL tạm thời
        const uploadPromises = uploadUrls.map((uploadUrl: string, index: number) => {
          const file = files[index];

          return fileUploadWithQueue.uploadToUrlWithQueue({
            file,
            presignedUrl: uploadUrl,
            taskTitle: `Upload: ${file.name}`,
            taskDescription: `Kích thước: ${(file.size / 1024).toFixed(1)} KB`,
          });
        });

        // Chờ tất cả các file upload xong
        await Promise.all(uploadPromises);

        // Cập nhật lại danh sách file sau khi upload xong
        queryClient.invalidateQueries({ queryKey: KNOWLEDGE_FILE_QUERY_KEYS.lists() });

        return uploadUrls;
      } catch (error) {
        console.error('Lỗi khi upload file:', error);
        throw error;
      }
    },
    [createFiles, fileUploadWithQueue, queryClient]
  );

  /**
   * Lấy MIME type từ tên file
   */
  const getMimeTypeFromFileName = (fileName: string): string => {
    const extension = fileName.split('.').pop()?.toLowerCase();

    switch (extension) {
      case 'pdf':
        return FileTypeEnum.PDF;
      case 'doc':
        return FileTypeEnum.DOC;
      case 'docx':
        return FileTypeEnum.DOCX;
      case 'json':
        return FileTypeEnum.JSON;
      case 'pptx':
        return FileTypeEnum.PPTX;
      case 'html':
        return FileTypeEnum.HTML;
      case 'txt':
        return FileTypeEnum.TXT;
      default:
        return 'application/octet-stream';
    }
  };

  return {
    uploadKnowledgeFile,
  };
};

export default useKnowledgeFileUpload;
