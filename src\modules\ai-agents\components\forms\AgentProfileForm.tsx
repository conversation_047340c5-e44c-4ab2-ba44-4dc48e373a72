import React, { useCallback } from 'react';
import { use<PERSON><PERSON>, Controller } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';

// Shared components
import Button from '@/shared/components/common/Button';
import Input from '@/shared/components/common/Input';
import Textarea from '@/shared/components/common/Textarea';
import Select from '@/shared/components/common/Select';

// Module schemas and types
import { agentProfileSchema, AgentProfileFormData } from '../../schemas';

/**
 * Props cho AgentProfileForm
 * ✅ ĐÚNG: Props suffix theo quy tắc ProductGuide.md
 */
interface AgentProfileFormProps {
  initialData?: Partial<AgentProfileFormData>;
  onSubmit: (data: AgentProfileFormData) => void | Promise<void>;
  isLoading?: boolean;
  disabled?: boolean;
}

/**
 * Component form cho Agent Profile
 * <PERSON><PERSON> thủ quy tắc ProductGuide.md - Section 5: FORM & VALIDATION
 */
export const AgentProfileForm: React.FC<AgentProfileFormProps> = ({
  initialData,
  onSubmit,
  isLoading = false,
  disabled = false,
}) => {
  // ✅ ĐÚNG: React Hook Form + Zod validation
  const {
    control,
    handleSubmit,
    formState: { isSubmitting },
    reset,
  } = useForm<AgentProfileFormData>({
    resolver: zodResolver(agentProfileSchema),
    defaultValues: {
      name: '',
      description: '',
      avatar: '',
      gender: undefined,
      birthDate: '',
      education: '',
      language: '',
      country: '',
      position: '',
      skills: [],
      personality: '',
      ...initialData,
    },
  });

  // ✅ ĐÚNG: useCallback cho functions truyền xuống
  const handleFormSubmit = useCallback(
    async (data: AgentProfileFormData) => {
      try {
        await onSubmit(data);
        // Reset form sau khi submit thành công (nếu không có initialData)
        if (!initialData) {
          reset();
        }
      } catch (error) {
        console.error('Form submission error:', error);
        // Error đã được handle ở parent component
      }
    },
    [onSubmit, initialData, reset]
  );

  // Gender options
  const genderOptions = [
    { value: 'male', label: 'Nam' },
    { value: 'female', label: 'Nữ' },
    { value: 'other', label: 'Khác' },
  ];

  const isFormDisabled = disabled || isLoading || isSubmitting;

  return (
    <form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-6">
      {/* Basic Information */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold">Thông tin cơ bản</h3>

        <div className="grid grid-cols-12 gap-4">
          {/* Avatar - 2 columns */}
          <div className="col-span-12 md:col-span-2">
            <Controller
              name="avatar"
              control={control}
              render={({ field, fieldState }) => (
                <div className="space-y-2">
                  <label className="block text-sm font-medium">Avatar</label>
                  <div className="flex items-center space-x-4">
                    {field.value && (
                      <img
                        src={field.value}
                        alt="Avatar preview"
                        className="w-16 h-16 rounded-full object-cover"
                      />
                    )}
                    <Input
                      {...field}
                      placeholder="URL avatar"
                      error={fieldState.error?.message}
                      disabled={isFormDisabled}
                    />
                  </div>
                </div>
              )}
            />
          </div>

          {/* Name - 5 columns */}
          <div className="col-span-12 md:col-span-5">
            <Controller
              name="name"
              control={control}
              render={({ field, fieldState }) => (
                <Input
                  {...field}
                  label="Tên Agent *"
                  placeholder="Nhập tên agent"
                  error={fieldState.error?.message}
                  disabled={isFormDisabled}
                />
              )}
            />
          </div>

          {/* Birth Date - 5 columns */}
          <div className="col-span-12 md:col-span-5">
            <Controller
              name="birthDate"
              control={control}
              render={({ field, fieldState }) => (
                <Input
                  {...field}
                  type="date"
                  label="Ngày sinh"
                  error={fieldState.error?.message}
                  disabled={isFormDisabled}
                />
              )}
            />
          </div>
        </div>

        <div className="grid grid-cols-12 gap-4">
          {/* Gender - 4 columns */}
          <div className="col-span-12 md:col-span-4">
            <Controller
              name="gender"
              control={control}
              render={({ field, fieldState }) => (
                <Select
                  {...field}
                  value={field.value || ''}
                  label="Giới tính"
                  placeholder="Chọn giới tính"
                  options={genderOptions}
                  error={fieldState.error?.message}
                  disabled={isFormDisabled}
                />
              )}
            />
          </div>

          {/* Education - 4 columns */}
          <div className="col-span-12 md:col-span-4">
            <Controller
              name="education"
              control={control}
              render={({ field, fieldState }) => (
                <Input
                  {...field}
                  label="Học vấn"
                  placeholder="Nhập học vấn"
                  error={fieldState.error?.message}
                  disabled={isFormDisabled}
                />
              )}
            />
          </div>

          {/* Language - 4 columns */}
          <div className="col-span-12 md:col-span-4">
            <Controller
              name="language"
              control={control}
              render={({ field, fieldState }) => (
                <Input
                  {...field}
                  label="Ngôn ngữ"
                  placeholder="Nhập ngôn ngữ"
                  error={fieldState.error?.message}
                  disabled={isFormDisabled}
                />
              )}
            />
          </div>
        </div>

        <div className="grid grid-cols-12 gap-4">
          {/* Country - 4 columns */}
          <div className="col-span-12 md:col-span-4">
            <Controller
              name="country"
              control={control}
              render={({ field, fieldState }) => (
                <Input
                  {...field}
                  label="Quốc gia"
                  placeholder="Nhập quốc gia"
                  error={fieldState.error?.message}
                  disabled={isFormDisabled}
                />
              )}
            />
          </div>

          {/* Position - 4 columns */}
          <div className="col-span-12 md:col-span-4">
            <Controller
              name="position"
              control={control}
              render={({ field, fieldState }) => (
                <Input
                  {...field}
                  label="Vị trí"
                  placeholder="Nhập vị trí"
                  error={fieldState.error?.message}
                  disabled={isFormDisabled}
                />
              )}
            />
          </div>

          {/* Skills - 4 columns */}
          <div className="col-span-12 md:col-span-4">
            <Controller
              name="skills"
              control={control}
              render={({ field, fieldState }) => (
                <Input
                  {...field}
                  value={field.value?.join(', ') || ''}
                  onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                    const skills = e.target.value.split(',').map((s: string) => s.trim()).filter(Boolean);
                    field.onChange(skills);
                  }}
                  label="Kỹ năng"
                  placeholder="Nhập kỹ năng (phân cách bằng dấu phẩy)"
                  error={fieldState.error?.message}
                  disabled={isFormDisabled}
                />
              )}
            />
          </div>
        </div>

        {/* Description */}
        <div>
          <label className="block text-sm font-medium mb-2">Mô tả</label>
          <Controller
            name="description"
            control={control}
            render={({ field, fieldState }) => (
              <div>
                <Textarea
                  {...field}
                  placeholder="Nhập mô tả về agent"
                  rows={3}
                  disabled={isFormDisabled}
                />
                {fieldState.error && (
                  <p className="text-red-500 text-sm mt-1">{fieldState.error.message}</p>
                )}
              </div>
            )}
          />
        </div>

        {/* Personality */}
        <div>
          <label className="block text-sm font-medium mb-2">Tính cách</label>
          <Controller
            name="personality"
            control={control}
            render={({ field, fieldState }) => (
              <div>
                <Textarea
                  {...field}
                  placeholder="Mô tả tính cách của agent"
                  rows={3}
                  disabled={isFormDisabled}
                />
                {fieldState.error && (
                  <p className="text-red-500 text-sm mt-1">{fieldState.error.message}</p>
                )}
              </div>
            )}
          />
        </div>
      </div>

      {/* Submit Button */}
      <div className="flex justify-end space-x-4">
        <Button
          type="submit"
          variant="primary"
          isLoading={isSubmitting || isLoading}
          disabled={isFormDisabled}
        >
          {initialData ? 'Cập nhật' : 'Tạo mới'}
        </Button>
      </div>
    </form>
  );
};
