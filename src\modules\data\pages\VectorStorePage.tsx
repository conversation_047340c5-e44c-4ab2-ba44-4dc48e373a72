import React, { useState, useEffect, useMemo, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { Card, Table, ConfirmDeleteModal } from '@/shared/components/common';
import { ActionMenu, ActionMenuItem } from '@/shared/components/common';
import { SortOrder } from '@/shared/components/common/Table/types';
import { SortDirection } from '@/shared/dto/request/query.dto';

import MenuIconBar, { ColumnVisibility } from '@/modules/components/menu-bar/MenuIconBar';
import SlideInForm from '@/shared/components/common/SlideInForm';
import useSlideForm from '@/shared/hooks/useSlideForm';
import { NotificationUtil } from '@/shared/utils/notification';
import AssignFilesToVectorStoreForm from '@/modules/data/components/forms/AssignFilesToVectorStoreForm';
import VectorStoreForm, {
  VectorStoreFormValues,
} from '@/modules/data/components/forms/VectorStoreForm';
import VectorStoreDetailView from '@/modules/data/components/VectorStoreDetailView';

import {
  useVectorStores,
  useCreateVectorStore,
  useDeleteMultipleVectorStores,
  useAssignFilesToVectorStore,
} from '@/modules/data/knowledge-files/hooks/useVectorStoreQuery';
import {
  VectorStoreResponseDto,
  QueryVectorStoreDto,
  CreateVectorStoreDto,
} from '@/modules/data/knowledge-files/types/knowledge-files.types';
import { formatDate } from '@/shared/utils/format';

// Đã chuyển component form sang file riêng

// Đã thay thế bằng AssignFilesToVectorStoreForm

/**
 * Trang quản lý Vector Store
 */
const VectorStorePage: React.FC = () => {
  const { t } = useTranslation('data');
  const [vectorStores, setVectorStores] = useState<VectorStoreResponseDto[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [totalItems, setTotalItems] = useState(0);
  const [showBulkDeleteConfirm, setShowBulkDeleteConfirm] = useState(false);
  const [selectedVectorStoreIds, setSelectedVectorStoreIds] = useState<string[]>([]);
  const [selectedVectorStoreId, setSelectedVectorStoreId] = useState<string>('');
  const [isAssigning, setIsAssigning] = useState(false);

  // State cho tìm kiếm
  const [searchTerm, setSearchTerm] = useState('');

  // State cho sắp xếp
  const [sortBy, setSortBy] = useState<string>('createdAt');
  const [sortDirection, setSortDirection] = useState<SortDirection>(SortDirection.DESC);

  // State cho hiển thị cột
  const [visibleColumns, setVisibleColumns] = useState<ColumnVisibility[]>([]);

  // Sử dụng hook animation cho form thêm mới
  const { isVisible, showForm, hideForm } = useSlideForm();

  // Sử dụng hook animation cho form gán file
  const {
    isVisible: isAssignFormVisible,
    showForm: showAssignForm,
    hideForm: hideAssignForm,
  } = useSlideForm();

  // Sử dụng hook animation cho form chi tiết
  const {
    isVisible: isDetailFormVisible,
    showForm: showDetailForm,
    hideForm: hideDetailForm,
  } = useSlideForm();

  // Tạo query params cho API
  const queryParams = useMemo<QueryVectorStoreDto>(() => {
    const params: QueryVectorStoreDto = {
      page: currentPage,
      limit: itemsPerPage,
      search: searchTerm,
      sortBy: sortBy,
      sortDirection: sortDirection,
    };

    return params;
  }, [currentPage, itemsPerPage, searchTerm, sortBy, sortDirection]);

  // Hooks để gọi API
  const {
    data: vectorStoresData,
    isLoading: isLoadingVectorStores,
    error: vectorStoresError,
  } = useVectorStores(queryParams);

  const { mutateAsync: createVectorStore } = useCreateVectorStore();
  const { mutateAsync: deleteMultipleVectorStores } = useDeleteMultipleVectorStores();
  const { mutateAsync: assignFilesToVectorStore } =
    useAssignFilesToVectorStore(selectedVectorStoreId);

  // Cập nhật state khi có dữ liệu từ API
  useEffect(() => {
    if (vectorStoresData) {
      setVectorStores(vectorStoresData.items);
      setTotalItems(vectorStoresData.meta.totalItems);
    }

    setIsLoading(isLoadingVectorStores);
  }, [vectorStoresData, vectorStoresError, isLoadingVectorStores]);

  // Xử lý thay đổi trang
  const handlePageChange = useCallback(
    (page: number, newPageSize: number) => {
      setCurrentPage(page);
      if (newPageSize !== itemsPerPage) {
        setItemsPerPage(newPageSize);
        setCurrentPage(1); // Reset về trang 1 khi thay đổi số mục trên trang
      }
    },
    [itemsPerPage]
  );

  // Xử lý tìm kiếm
  const handleSearch = useCallback((term: string) => {
    setSearchTerm(term);
    setCurrentPage(1); // Reset về trang 1 khi tìm kiếm
  }, []);

  // Xử lý thay đổi sắp xếp
  const handleSortChange = useCallback((column: string | null, order: SortOrder) => {
    setSortBy(column || '');
    setSortDirection(order === 'asc' ? SortDirection.ASC : SortDirection.DESC);
  }, []);

  // Xử lý hủy xóa nhiều
  const handleCancelBulkDelete = useCallback(() => {
    setShowBulkDeleteConfirm(false);
  }, []);

  // Xử lý xác nhận xóa nhiều
  const handleConfirmBulkDelete = useCallback(async () => {
    if (selectedVectorStoreIds.length === 0) return;

    try {
      // Xóa nhiều Vector Store cùng lúc với một request
      await deleteMultipleVectorStores({ storeIds: selectedVectorStoreIds });

      setShowBulkDeleteConfirm(false);
      setSelectedVectorStoreIds([]);

      NotificationUtil.success({
        message: t('data:vectorStore.bulkDeleteSuccess', 'Xóa các Vector Store đã chọn thành công'),
        duration: 3000,
      });
    } catch (error) {
      console.error('Error deleting multiple Vector Stores:', error);
      NotificationUtil.error({
        message: t('data:vectorStore.bulkDeleteError', 'Lỗi khi xóa các Vector Store đã chọn'),
        duration: 3000,
      });
    }
  }, [selectedVectorStoreIds, deleteMultipleVectorStores, t]);

  // Xử lý thay đổi hiển thị cột
  const handleColumnVisibilityChange = useCallback((columns: ColumnVisibility[]) => {
    setVisibleColumns(columns);
  }, []);

  // Xử lý hiển thị form gán file
  const handleShowAssignFilesForm = useCallback(
    (vectorStoreId: string) => {
      console.log('🔍 handleShowAssignFilesForm called with:', vectorStoreId);
      console.log('🔍 Before - isAssignFormVisible:', isAssignFormVisible);
      setSelectedVectorStoreId(vectorStoreId);
      showAssignForm();
      console.log('🔍 After showAssignForm called');
    },
    [showAssignForm, isAssignFormVisible]
  );

  // Xử lý hiển thị form chi tiết
  const handleShowDetailForm = useCallback(
    (vectorStoreId: string) => {
      setSelectedVectorStoreId(vectorStoreId);
      showDetailForm();
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [showDetailForm, isDetailFormVisible]
  );

  // Xử lý submit form tạo Vector Store
  const handleSubmitCreateVectorStore = useCallback(
    async (values: VectorStoreFormValues) => {
      try {
        // Chuẩn bị dữ liệu cho API
        const vectorStoreData: CreateVectorStoreDto = {
          name: values.name,
        };

        // Gọi API tạo Vector Store
        setIsLoading(true);
        await createVectorStore(vectorStoreData);
        setIsLoading(false);
        hideForm();

        NotificationUtil.success({
          message: t('data:vectorStore.createSuccess', 'Tạo Vector Store thành công'),
          duration: 3000,
        });
      } catch (error) {
        console.error('Error creating Vector Store:', error);
        NotificationUtil.error({
          message: t('data:vectorStore.createError', 'Lỗi khi tạo Vector Store'),
          duration: 3000,
        });
      }
    },
    [createVectorStore, hideForm, t]
  );

  // Xử lý submit form gán file
  const handleSubmitAssignFiles = useCallback(
    async (values: Record<string, unknown>) => {
      try {
        setIsAssigning(true);
        const fileIds = values['fileIds'] as string[];

        // Gọi API gán file
        await assignFilesToVectorStore({
          fileIds: Array.isArray(fileIds) ? fileIds : [fileIds],
        });

        // Đóng form và hiển thị thông báo thành công
        hideAssignForm();
        NotificationUtil.success({
          message: t('data:vectorStore.assignSuccess', 'Gán file thành công'),
          duration: 3000,
        });
      } catch (error) {
        console.error('Error assigning files to Vector Store:', error);
        NotificationUtil.error({
          message: t('data:vectorStore.assignError', 'Lỗi khi gán file'),
          duration: 3000,
        });
      } finally {
        setIsAssigning(false);
      }
    },
    [assignFilesToVectorStore, hideAssignForm, t]
  );

  // Định nghĩa các cột cho bảng
  const columns = useMemo(() => {
    const allColumns = [
      {
        key: 'storeName',
        title: t('data:vectorStore.table.name', 'Tên Vector Store'),
        dataIndex: 'storeName',
        width: '25%',
        sortable: true,
      },
      {
        key: 'files',
        title: t('data:vectorStore.table.files', 'Số file'),
        dataIndex: 'files',
        width: '15%',
        sortable: true,
      },
      {
        key: 'size',
        title: t('data:vectorStore.table.size', 'Dung lượng'),
        dataIndex: 'size',
        width: '15%',
        sortable: true,
        render: (value: unknown) => {
          const size = Number(value);
          if (size < 1024) return `${size} B`;
          if (size < 1024 * 1024) return `${(size / 1024).toFixed(2)} KB`;
          return `${(size / (1024 * 1024)).toFixed(2)} MB`;
        },
      },
      {
        key: 'agents',
        title: t('data:vectorStore.table.agents', 'Số agents'),
        dataIndex: 'agents',
        width: '15%',
        sortable: true,
      },
      {
        key: 'createdAt',
        title: t('data:common.createdAt', 'Ngày tạo'),
        dataIndex: 'createdAt',
        width: '15%',
        sortable: true,
        render: (value: unknown) => <span>{formatDate(value as number)}</span>,
      },
      {
        key: 'actions',
        title: t('common:actions', 'Thao tác'),
        render: (_: unknown, record: VectorStoreResponseDto) => {
          // Tạo danh sách các action items
          const actionItems: ActionMenuItem[] = [
            {
              id: 'view',
              label: t('data:vectorStore.detail', 'Xem chi tiết'),
              icon: 'eye',
              onClick: () => handleShowDetailForm(record.storeId),
            },
            {
              id: 'assign',
              label: t('data:vectorStore.assignFiles'),
              icon: 'file',
              onClick: () => handleShowAssignFilesForm(record.storeId),
            },
          ];

          return (
            <ActionMenu
              items={actionItems}
              menuTooltip={t('common:moreActions', 'Thêm thao tác')}
              iconSize="sm"
              iconVariant="default"
              placement="bottom"
              menuWidth="250px"
              showAllInMenu={true}
              preferRight={true}
            />
          );
        },
      },
    ];

    // Lọc các cột dựa trên visibleColumns
    if (visibleColumns.length === 0) {
      return allColumns;
    }

    const visibleColumnIds = visibleColumns.filter(col => col.visible).map(col => col.id);

    // Luôn hiển thị cột actions
    return allColumns.filter(col => col.key === 'actions' || visibleColumnIds.includes(col.key));
  }, [t, visibleColumns, handleShowAssignFilesForm, handleShowDetailForm]);

  // Lọc các cột hiển thị
  const filteredColumns = useMemo(() => {
    // Nếu chưa có visibleColumns, hiển thị tất cả
    if (visibleColumns.length === 0) {
      // Tạo visibleColumns từ columns
      setVisibleColumns([
        { id: 'all', label: 'Tất cả', visible: true },
        ...columns.map(col => ({
          id: col.key,
          label: typeof col.title === 'string' ? col.title : col.key,
          visible: true,
        })),
      ]);
      return columns;
    }

    // Nếu "Tất cả" được chọn, hiển thị tất cả
    const allSelected = visibleColumns.find(col => col.id === 'all')?.visible;
    if (allSelected) {
      return columns;
    }

    // Lọc theo các cột được chọn
    return columns.filter(
      col => col.key === 'actions' || visibleColumns.find(vc => vc.id === col.key)?.visible
    );
  }, [columns, visibleColumns]);

  return (
    <div>
      {/* Thêm MenuIconBar */}
      <MenuIconBar
        onSearch={handleSearch}
        onAdd={() => showForm()}
        items={[]}
        additionalIcons={[
          {
            icon: 'trash',
            tooltip: t('common:bulkDelete', 'Xóa nhiều'),
            variant: 'primary',
            onClick: () => {
              if (selectedVectorStoreIds.length > 0) {
                setShowBulkDeleteConfirm(true);
              } else {
                NotificationUtil.info({
                  message: t(
                    'data:vectorStore.selectToDelete',
                    'Vui lòng chọn ít nhất một Vector Store để xóa'
                  ),
                  duration: 3000,
                });
              }
            },
            condition: selectedVectorStoreIds.length > 0,
          },
        ]}
        onColumnVisibilityChange={handleColumnVisibilityChange}
        columns={visibleColumns}
        showDateFilter={false}
        showColumnFilter={true}
      />

      {/* SlideInForm cho form thêm mới */}
      <SlideInForm isVisible={isVisible}>
        <VectorStoreForm
          onSubmit={handleSubmitCreateVectorStore}
          onCancel={hideForm}
          isLoading={isLoading}
        />
      </SlideInForm>

      {/* SlideInForm cho form gán file */}
      <SlideInForm isVisible={isAssignFormVisible}>
        {selectedVectorStoreId && (
          <AssignFilesToVectorStoreForm
            onSubmit={handleSubmitAssignFiles}
            onCancel={hideAssignForm}
            vectorStoreId={selectedVectorStoreId}
            isLoading={isAssigning}
          />
        )}
      </SlideInForm>

      {/* SlideInForm cho form chi tiết */}
      <SlideInForm isVisible={isDetailFormVisible}>
        {selectedVectorStoreId && (
          <VectorStoreDetailView
            vectorStoreId={selectedVectorStoreId}
            onClose={hideDetailForm}
            onAssignFiles={handleShowAssignFilesForm}
          />
        )}
      </SlideInForm>

      <Card className="overflow-hidden">
        <Table<VectorStoreResponseDto>
          columns={filteredColumns}
          data={vectorStores}
          rowKey="storeId"
          loading={isLoading}
          sortable={true}
          onSortChange={handleSortChange}
          rowSelection={{
            selectedRowKeys: selectedVectorStoreIds,
            onChange: (keys: React.Key[]) => setSelectedVectorStoreIds(keys as string[]),
          }}
          defaultSort={{
            column: sortBy || '',
            order: sortDirection === SortDirection.ASC ? 'asc' : 'desc',
          }}
          pagination={{
            current: currentPage,
            pageSize: itemsPerPage,
            total: totalItems,
            onChange: handlePageChange,
            showSizeChanger: true,
            pageSizeOptions: [10, 20, 50, 100],
            showFirstLastButtons: true,
            showPageInfo: true,
          }}
        />
      </Card>

      {/* Modal xác nhận xóa nhiều */}
      <ConfirmDeleteModal
        isOpen={showBulkDeleteConfirm}
        onClose={handleCancelBulkDelete}
        onConfirm={handleConfirmBulkDelete}
        title={t('data:vectorStore.bulkDeleteTitle', 'Xóa các Vector Store đã chọn')}
        message={t(
          'data:vectorStore.bulkDeleteMessage',
          'Bạn có chắc chắn muốn xóa {{count}} Vector Store đã chọn không?',
          { count: selectedVectorStoreIds.length }
        )}
      />
    </div>
  );
};

export default VectorStorePage;
