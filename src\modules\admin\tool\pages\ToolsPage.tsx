/* eslint-disable @typescript-eslint/no-explicit-any */
import React, { useState, useCallback, useMemo } from 'react';
import { useTranslation } from 'react-i18next';

import { Card, Modal, Typography, Button, Pagination } from '@/shared/components/common';
import { NotificationUtil } from '@/shared/utils/notification';
import MenuIconBar from '@/modules/components/menu-bar/MenuIconBar';
import SlideInForm from '@/shared/components/common/SlideInForm';
import useSlideForm from '@/shared/hooks/useSlideForm';
import { ToolGrid, ToolForm } from '../components';
import {
  useAdminTools,
  useCreateAdminTool,
  useDeleteAdminTool,
  useAdminToolDetail,
  useUpdateAdminTool,
} from '../hooks';
import {
  useUpdateAdminToolVersion,
  useDeleteAdminToolVersion,
  useCreateAdminToolVersion,
} from '../hooks/useToolVersion';
import {
  ToolListItem,
  ToolStatus,
  ToolSortBy,
  CreateToolParams,
  UpdateToolParams,
  UpdateToolVersionParams,
  CreateToolVersionParams,
} from '../types/tool.types';

/**
 * Trang hiển thị danh sách tools dạng grid
 */
const ToolsPage: React.FC = () => {
  const { t } = useTranslation(['admin']);

  // State cho tìm kiếm và lọc
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState<ToolStatus | 'all'>('all');

  // State cho phân trang
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);

  // State cho xóa tool
  const [toolToDelete, setToolToDelete] = useState<ToolListItem | null>(null);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);

  // State cho xem/chỉnh sửa tool
  const [toolToView, setToolToView] = useState<ToolListItem | null>(null);
  const [isEditMode, setIsEditMode] = useState(false);

  // Sử dụng hook animation cho form tạo mới
  const {
    isVisible: isCreateFormVisible,
    showForm: showCreateForm,
    hideForm: hideCreateForm,
  } = useSlideForm();

  // Sử dụng hook animation cho form xem/chỉnh sửa
  const {
    isVisible: isEditFormVisible,
    showForm: showEditForm,
    hideForm: hideEditForm,
  } = useSlideForm();

  // Tạo query params cho API
  const queryParams = useMemo(
    () => ({
      page: currentPage,
      limit: itemsPerPage,
      search: searchTerm || undefined,
      status: filterStatus !== 'all' ? filterStatus : undefined,
      sortBy: ToolSortBy.CREATED_AT,
      sortDirection: 'DESC' as const,
    }),
    [currentPage, itemsPerPage, searchTerm, filterStatus]
  );

  // Hooks để gọi API
  const { data: toolsData, isLoading } = useAdminTools(queryParams);
  const createToolMutation = useCreateAdminTool();
  const deleteToolMutation = useDeleteAdminTool();
  const updateToolMutation = useUpdateAdminTool();
  const updateVersionMutation = useUpdateAdminToolVersion();
  const deleteVersionMutation = useDeleteAdminToolVersion();
  const createVersionMutation = useCreateAdminToolVersion();
  const { data: toolDetail, isLoading: isLoadingDetail } = useAdminToolDetail(toolToView?.id || '');

  const createTool = createToolMutation.mutateAsync;
  const isCreating = createToolMutation.isPending || false;
  const deleteTool = deleteToolMutation.mutateAsync;
  const isDeleting = deleteToolMutation.isPending || false;
  const updateTool = updateToolMutation.mutateAsync;
  const isUpdating = updateToolMutation.isPending || false;
  const updateVersion = updateVersionMutation.mutateAsync;
  const isUpdatingVersion = updateVersionMutation.isPending || false;
  const deleteVersion = deleteVersionMutation.mutateAsync;
  const isDeletingVersion = deleteVersionMutation.isPending || false;
  const createVersion = createVersionMutation.mutateAsync;
  const isCreatingVersion = createVersionMutation.isPending || false;

  // Xử lý tìm kiếm
  const handleSearch = useCallback((term: string) => {
    setSearchTerm(term);
    setCurrentPage(1); // Reset về trang 1 khi tìm kiếm
  }, []);

  // Xử lý lọc theo trạng thái
  const handleFilterStatus = useCallback((status: string) => {
    setFilterStatus(status as ToolStatus | 'all');
    setCurrentPage(1); // Reset về trang 1 khi lọc
  }, []);

  // Xử lý thay đổi trang
  const handlePageChange = useCallback((page: number) => {
    setCurrentPage(page);
  }, []);

  // Xử lý thay đổi số mục trên trang
  const handleItemsPerPageChange = useCallback((newItemsPerPage: number) => {
    setItemsPerPage(newItemsPerPage);
    setCurrentPage(1); // Reset về trang 1 khi thay đổi số mục trên trang
  }, []);

  // Xử lý xem chi tiết tool
  const handleViewTool = useCallback(
    (tool: ToolListItem) => {
      // Đóng form create nếu đang mở
      if (isCreateFormVisible) {
        hideCreateForm();
      }
      setToolToView(tool);
      setIsEditMode(false);
      showEditForm();
    },
    [showEditForm, isCreateFormVisible, hideCreateForm]
  );

  // Xử lý chỉnh sửa tool
  const handleEditTool = useCallback(
    (tool: ToolListItem) => {
      // Đóng form create nếu đang mở
      if (isCreateFormVisible) {
        hideCreateForm();
      }
      setToolToView(tool);
      setIsEditMode(true);
      showEditForm();
    },
    [showEditForm, isCreateFormVisible, hideCreateForm]
  );

  // Xử lý hủy xóa
  const handleCancelDelete = useCallback(() => {
    setShowDeleteConfirm(false);
    setToolToDelete(null);
  }, []);

  // Xử lý xác nhận xóa
  const handleConfirmDelete = useCallback(async () => {
    if (!toolToDelete) return;

    try {
      await deleteTool(toolToDelete.id);
      setShowDeleteConfirm(false);
      setToolToDelete(null);
      NotificationUtil.success({
        message: t('admin:tool.deleteSuccess', 'Tool đã được xóa thành công!'),
        duration: 3000,
      });
    } catch (error) {
      console.error('Error deleting tool:', error);
      NotificationUtil.error({
        message: t('admin:tool.deleteError', 'Có lỗi xảy ra khi xóa tool!'),
        duration: 3000,
      });
    }
  }, [toolToDelete, deleteTool, t]);

  // Xử lý submit form tạo tool
  const handleSubmitCreateTool = useCallback(
    (values: CreateToolParams | UpdateToolParams) => {
      try {
        if ('toolName' in values) {
          // Nếu là CreateToolParams
          createTool(values)
            .then(() => {
              hideCreateForm();
              NotificationUtil.success({
                message: t('admin:tool.createSuccess', 'Tool đã được tạo thành công!'),
                duration: 3000,
              });
            })
            .catch(error => {
              console.error('Error creating tool:', error);
              NotificationUtil.error({
                message: t(
                  'admin:tool.createError',
                  'Có lỗi xảy ra khi tạo tool. Vui lòng thử lại.'
                ),
                duration: 5000,
              });
            });
        }
      } catch (error) {
        console.error('Error creating tool:', error);
        NotificationUtil.error({
          message: t('admin:tool.createError', 'Có lỗi xảy ra khi tạo tool. Vui lòng thử lại.'),
          duration: 5000,
        });
      }
    },
    [createTool, hideCreateForm, t]
  );

  // Xử lý submit form chỉnh sửa tool
  const handleSubmitEditTool = useCallback(
    (values: CreateToolParams | UpdateToolParams) => {
      if (!toolToView) return;

      try {
        updateTool({ id: toolToView.id, data: values as UpdateToolParams })
          .then(() => {
            hideEditForm();
            setToolToView(null);
            NotificationUtil.success({
              message: t('admin:tool.updateSuccess', 'Tool đã được cập nhật thành công!'),
              duration: 3000,
            });
          })
          .catch(error => {
            console.error('Error updating tool:', error);
            NotificationUtil.error({
              message: t(
                'admin:tool.updateError',
                'Có lỗi xảy ra khi cập nhật tool. Vui lòng thử lại.'
              ),
              duration: 5000,
            });
          });
      } catch (error) {
        console.error('Error updating tool:', error);
        NotificationUtil.error({
          message: t(
            'admin:tool.updateError',
            'Có lỗi xảy ra khi cập nhật tool. Vui lòng thử lại.'
          ),
          duration: 5000,
        });
      }
    },
    [updateTool, hideEditForm, toolToView, t]
  );

  // Xử lý submit form chỉnh sửa version
  const handleSubmitEditVersion = useCallback(
    (versionId: string, values: UpdateToolVersionParams) => {
      if (!toolToView) return;

      try {
        updateVersion({
          toolId: toolToView.id,
          versionId: versionId,
          data: values,
        })
          .then(() => {
            NotificationUtil.success({
              message: t(
                'admin:tool.updateVersionSuccess',
                'Phiên bản đã được cập nhật thành công!'
              ),
              duration: 3000,
            });
          })
          .catch(error => {
            console.error('Error updating version:', error);
            NotificationUtil.error({
              message: t(
                'admin:tool.updateVersionError',
                'Có lỗi xảy ra khi cập nhật phiên bản. Vui lòng thử lại.'
              ),
              duration: 5000,
            });
          });
      } catch (error) {
        console.error('Error updating version:', error);
        NotificationUtil.error({
          message: t(
            'admin:tool.updateVersionError',
            'Có lỗi xảy ra khi cập nhật phiên bản. Vui lòng thử lại.'
          ),
          duration: 5000,
        });
      }
    },
    [updateVersion, toolToView, t]
  );

  // Xử lý submit form xóa version
  const handleSubmitDeleteVersion = useCallback(
    (versionId: string) => {
      if (!toolToView) return;

      try {
        deleteVersion({
          toolId: toolToView.id,
          versionId: versionId,
        })
          .then(() => {
            NotificationUtil.success({
              message: t('admin:tool.deleteVersionSuccess', 'Phiên bản đã được xóa thành công!'),
              duration: 3000,
            });
          })
          .catch(error => {
            console.error('Error deleting version:', error);
            NotificationUtil.error({
              message: t(
                'admin:tool.deleteVersionError',
                'Có lỗi xảy ra khi xóa phiên bản. Vui lòng thử lại.'
              ),
              duration: 5000,
            });
          });
      } catch (error) {
        console.error('Error deleting version:', error);
        NotificationUtil.error({
          message: t(
            'admin:tool.deleteVersionError',
            'Có lỗi xảy ra khi xóa phiên bản. Vui lòng thử lại.'
          ),
          duration: 5000,
        });
      }
    },
    [deleteVersion, toolToView, t]
  );

  // Xử lý submit form tạo version mới
  const handleSubmitCreateVersion = useCallback(
    (values: CreateToolVersionParams) => {
      if (!toolToView) return;

      try {
        createVersion({
          toolId: toolToView.id,
          data: values,
        })
          .then(() => {
            NotificationUtil.success({
              message: t(
                'admin:tool.createVersionSuccess',
                'Phiên bản mới đã được tạo thành công!'
              ),
              duration: 3000,
            });
          })
          .catch(error => {
            console.error('Error creating version:', error);
            NotificationUtil.error({
              message: t(
                'admin:tool.createVersionError',
                'Có lỗi xảy ra khi tạo phiên bản mới. Vui lòng thử lại.'
              ),
              duration: 5000,
            });
          });
      } catch (error) {
        console.error('Error creating version:', error);
        NotificationUtil.error({
          message: t(
            'admin:tool.createVersionError',
            'Có lỗi xảy ra khi tạo phiên bản mới. Vui lòng thử lại.'
          ),
          duration: 5000,
        });
      }
    },
    [createVersion, toolToView, t]
  );

  // Xử lý đóng form xem/chỉnh sửa
  const handleCloseEditForm = useCallback(() => {
    hideEditForm();
    setToolToView(null);
    setIsEditMode(false);
  }, [hideEditForm]);

  // Xử lý xóa tool
  const handleDeleteTool = useCallback((tool: ToolListItem) => {
    setToolToDelete(tool);
    setShowDeleteConfirm(true);
  }, []);

  return (
    <div className="space-y-4">
      {/* Menu bar */}
      <MenuIconBar
        onSearch={handleSearch}
        onAdd={() => {
          // Đóng form edit nếu đang mở
          if (isEditFormVisible) {
            hideEditForm();
            setToolToView(null);
            setIsEditMode(false);
          }
          showCreateForm();
        }}
        items={[
          {
            id: 'all',
            label: t('admin:common.all', 'All'),
            icon: 'list',
            onClick: () => handleFilterStatus('all'),
          },
          {
            id: 'draft',
            label: t('admin:tool.status.draft', 'Draft'),
            icon: 'file',
            onClick: () => handleFilterStatus(ToolStatus.DRAFT),
          },
          {
            id: 'approved',
            label: t('admin:tool.status.approved', 'Approved'),
            icon: 'check-circle',
            onClick: () => handleFilterStatus(ToolStatus.APPROVED),
          },
          {
            id: 'deprecated',
            label: t('admin:tool.status.deprecated', 'Deprecated'),
            icon: 'archive',
            onClick: () => handleFilterStatus(ToolStatus.DEPRECATED),
          },
        ]}
      />

      {/* SlideInForm cho form thêm mới */}
      <SlideInForm isVisible={isCreateFormVisible}>
        <ToolForm
          onSubmit={handleSubmitCreateTool}
          onCancel={hideCreateForm}
          isLoading={isCreating}
        />
      </SlideInForm>

      {/* SlideInForm cho form xem/chỉnh sửa */}
      <SlideInForm isVisible={isEditFormVisible}>
        {toolToView && (
          <ToolForm
            initialValues={toolDetail || (toolToView as any)}
            onSubmit={handleSubmitEditTool}
            onSubmitVersion={handleSubmitEditVersion}
            onCreateVersion={handleSubmitCreateVersion}
            onDeleteVersion={handleSubmitDeleteVersion}
            onCancel={handleCloseEditForm}
            isLoading={
              isUpdating ||
              isLoadingDetail ||
              isUpdatingVersion ||
              isDeletingVersion ||
              isCreatingVersion
            }
            isEdit={isEditMode}
            readOnly={!isEditMode}
          />
        )}
      </SlideInForm>

      {/* Hiển thị danh sách tool */}
      {toolsData?.items && toolsData.items.length > 0 ? (
        <>
          <ToolGrid
            tools={toolsData.items}
            onViewTool={handleViewTool}
            onEditTool={handleEditTool}
            onDeleteTool={handleDeleteTool}
          />

          {/* Phân trang */}
          {toolsData.meta && toolsData.meta.totalItems > 0 && (
            <div className="mt-6 flex justify-end">
              <Pagination
                variant="simple"
                currentPage={currentPage}
                totalItems={toolsData.meta.totalItems}
                itemsPerPage={itemsPerPage}
                onPageChange={handlePageChange}
                onItemsPerPageChange={handleItemsPerPageChange}
                itemsPerPageOptions={[10, 20, 50, 100]}
                showFirstLastButtons={false}
                showItemsPerPageSelector={true}
                showPageInfo={false}
                size="md"
              />
            </div>
          )}
        </>
      ) : (
        <Card className="p-8">
          <div className="flex flex-col items-center justify-center">
            <Typography variant="h6" className="text-gray-500 dark:text-gray-400">
              {isLoading
                ? t('admin:tool.loading', 'Loading...')
                : t('admin:tool.noTools', 'No tools found')}
            </Typography>
          </div>
        </Card>
      )}

      {/* Modal xác nhận xóa */}
      <Modal
        isOpen={showDeleteConfirm}
        onClose={handleCancelDelete}
        title={t('admin:tool.confirmDelete', 'Confirm Delete')}
        size="sm"
        footer={
          <div className="flex justify-end space-x-3">
            <Button variant="outline" onClick={handleCancelDelete}>
              {t('admin:tool.cancel', 'Cancel')}
            </Button>
            <Button variant="danger" onClick={handleConfirmDelete} disabled={isDeleting}>
              {isDeleting
                ? t('admin:tool.deleting', 'Deleting...')
                : t('admin:tool.delete', 'Delete')}
            </Button>
          </div>
        }
      >
        <div className="py-4">
          <Typography>
            {t('admin:tool.confirmDeleteMessage', 'Are you sure you want to delete this tool?')}
          </Typography>
          {toolToDelete && (
            <Typography variant="body2" className="mt-2 font-semibold">
              {toolToDelete.name}
            </Typography>
          )}
        </div>
      </Modal>
    </div>
  );
};

export default ToolsPage;
