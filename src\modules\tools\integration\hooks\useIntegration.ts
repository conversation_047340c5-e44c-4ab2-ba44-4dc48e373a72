import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { integrationService } from '../services/integration.service';
import {
  BaseQueryParams,
  IntegrateFromOpenApiParams as TypeIntegrateFromOpenApiParams,
  UpdateBaseUrlParams as TypeUpdateBaseUrlParams,
  UpdateToolAuthParams as TypeUpdateToolAuthParams,
  OpenApiSpec,
} from '../types/integration.types';
import {
  integrationQuerySchema,
  IntegrateFromOpenApiParams,
  UpdateBaseUrlParams,
  UpdateToolAuthParams,
} from '../schemas/integration.schema';

// Query keys
export const INTEGRATION_QUERY_KEYS = {
  all: ['integrations'] as const,
  lists: () => [...INTEGRATION_QUERY_KEYS.all, 'list'] as const,
  list: (params: BaseQueryParams) => [...INTEGRATION_QUERY_KEYS.lists(), params] as const,
  details: () => [...INTEGRATION_QUERY_KEYS.all, 'detail'] as const,
  detail: (id: string) => [...INTEGRATION_QUERY_KEYS.details(), id] as const,
};

/**
 * Hook để lấy danh sách integration tools
 * @param params Tham số truy vấn
 * @returns Query object
 */
export const useIntegrationTools = (params?: BaseQueryParams) => {
  // Validate và transform params
  const validParams = params ? integrationQuerySchema.parse(params) : undefined;

  return useQuery({
    queryKey: INTEGRATION_QUERY_KEYS.list(validParams || {}),
    queryFn: () => integrationService.getCustomTools(validParams || {}),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

/**
 * Hook để lấy thông tin chi tiết integration tool
 * @param toolId ID của tool
 * @returns Query object
 */
export const useIntegrationToolDetail = (toolId?: string) => {
  return useQuery({
    queryKey: INTEGRATION_QUERY_KEYS.detail(toolId || ''),
    queryFn: () => integrationService.getCustomToolById(toolId || ''),
    enabled: !!toolId,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

/**
 * Hàm validate OpenAPI spec
 * @param spec OpenAPI spec cần kiểm tra
 * @returns OpenAPI spec đã được xác thực
 */
const validateOpenApiSpec = (spec: unknown): OpenApiSpec => {
  if (!spec || typeof spec !== 'object') {
    throw new Error('OpenAPI specification phải là một đối tượng');
  }

  const specObj = spec as Record<string, unknown>;

  // Kiểm tra các trường bắt buộc
  if (!specObj.openapi || typeof specObj.openapi !== 'string') {
    throw new Error('OpenAPI specification phải có trường "openapi" là một chuỗi');
  }

  if (!specObj.info || typeof specObj.info !== 'object') {
    throw new Error('OpenAPI specification phải có trường "info" là một đối tượng');
  }

  const infoObj = specObj.info as Record<string, unknown>;
  if (!infoObj.title || typeof infoObj.title !== 'string') {
    throw new Error('OpenAPI info phải có trường "title" là một chuỗi');
  }

  if (!infoObj['version'] || typeof infoObj['version'] !== 'string') {
    throw new Error('OpenAPI info phải có trường "version" là một chuỗi');
  }

  if (!specObj['paths'] || typeof specObj['paths'] !== 'object') {
    throw new Error('OpenAPI specification phải có trường "paths" là một đối tượng');
  }

  // Trả về spec đã xác thực
  return spec as OpenApiSpec;
};

/**
 * Hook để tích hợp từ OpenAPI
 * @returns Mutation object
 */
export function useIntegrateFromOpenApi() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (params: IntegrateFromOpenApiParams) => {
      // Chuyển đổi và xác thực OpenApiSpec
      const validatedSpec = validateOpenApiSpec(params.openapiSpec);

      // Tạo tham số hợp lệ với kiểu TypeIntegrateFromOpenApiParams
      const validParams: TypeIntegrateFromOpenApiParams = {
        openapiSpec: validatedSpec,
        ...(params.baseUrl && { baseUrl: params.baseUrl }),
        ...(params.authConfig && { authConfig: params.authConfig }),
      };

      return integrationService.integrateFromOpenApi(validParams);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: INTEGRATION_QUERY_KEYS.lists() });
    },
    onError: error => {
      console.error('Lỗi khi tích hợp OpenAPI:', error);
    },
  });
}

/**
 * Hook để xóa integration tool
 * @returns Mutation object
 */
export const useDeleteIntegrationTool = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (toolId: string) => {
      if (!toolId) throw new Error('Tool ID không được để trống');
      return integrationService.deleteCustomTool(toolId);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: INTEGRATION_QUERY_KEYS.lists() });
    },
    onError: error => {
      console.error('Lỗi khi xóa tool:', error);
    },
  });
};

/**
 * Hook để cập nhật base URL
 * @returns Mutation object
 */
export const useUpdateBaseUrl = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (params: UpdateBaseUrlParams) => {
      if (!params.toolId) throw new Error('Tool ID không được để trống');
      if (!params.baseUrl) throw new Error('Base URL không được để trống');

      const validParams: TypeUpdateBaseUrlParams = {
        toolId: params.toolId,
        baseUrl: params.baseUrl,
      };

      return integrationService.updateBaseUrl(validParams);
    },
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: INTEGRATION_QUERY_KEYS.lists() });
      queryClient.invalidateQueries({
        queryKey: INTEGRATION_QUERY_KEYS.detail(variables.toolId),
      });
    },
    onError: error => {
      console.error('Lỗi khi cập nhật base URL:', error);
    },
  });
};

/**
 * Hook để cập nhật auth config
 * @returns Mutation object
 */
export const useUpdateToolAuth = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (params: UpdateToolAuthParams) => {
      if (!params.toolId) throw new Error('Tool ID không được để trống');
      if (!params.authConfig) throw new Error('Auth config không được để trống');

      const validParams: TypeUpdateToolAuthParams = {
        toolId: params.toolId,
        authConfig: params.authConfig,
      };

      return integrationService.updateToolAuth(validParams);
    },
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: INTEGRATION_QUERY_KEYS.lists() });
      queryClient.invalidateQueries({
        queryKey: INTEGRATION_QUERY_KEYS.detail(variables.toolId),
      });
    },
    onError: error => {
      console.error('Lỗi khi cập nhật auth config:', error);
    },
  });
};

/**
 * Hook để toggle status của tool
 * @returns Mutation object
 */
export const useToggleIntegrationToolStatus = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (toolId: string) => {
      if (!toolId) throw new Error('Tool ID không được để trống');
      return integrationService.toggleToolStatus(toolId);
    },
    onSuccess: data => {
      queryClient.invalidateQueries({ queryKey: INTEGRATION_QUERY_KEYS.lists() });
      if (data && data.id) {
        queryClient.invalidateQueries({
          queryKey: INTEGRATION_QUERY_KEYS.detail(data.id),
        });
      }
    },
    onError: error => {
      console.error('Lỗi khi toggle trạng thái tool:', error);
    },
  });
};

/**
 * Hook để test connection
 * @returns Mutation object
 */
export const useTestConnection = () => {
  return useMutation({
    mutationFn: (toolId: string) => {
      if (!toolId) throw new Error('Tool ID không được để trống');
      return integrationService.testConnection(toolId);
    },
    onError: error => {
      console.error('Lỗi khi test kết nối:', error);
    },
  });
};

/**
 * Hook để refresh OpenAPI spec
 * @returns Mutation object
 */
export const useRefreshOpenApiSpec = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (toolId: string) => {
      if (!toolId) throw new Error('Tool ID không được để trống');
      return integrationService.refreshOpenApiSpec(toolId);
    },
    onSuccess: data => {
      queryClient.invalidateQueries({ queryKey: INTEGRATION_QUERY_KEYS.lists() });
      if (data && data.id) {
        queryClient.invalidateQueries({
          queryKey: INTEGRATION_QUERY_KEYS.detail(data.id),
        });
      }
    },
    onError: error => {
      console.error('Lỗi khi refresh OpenAPI spec:', error);
    },
  });
};

/**
 * Hook để export configuration
 * @returns Mutation object
 */
export const useExportConfiguration = () => {
  return useMutation({
    mutationFn: (toolId: string) => {
      if (!toolId) throw new Error('Tool ID không được để trống');
      return integrationService.exportConfiguration(toolId);
    },
    onError: error => {
      console.error('Lỗi khi export cấu hình:', error);
    },
  });
};

/**
 * Hook để import configuration
 * @returns Mutation object
 */
export const useImportConfiguration = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (config: Record<string, unknown>) => {
      if (!config || Object.keys(config).length === 0) {
        throw new Error('Cấu hình không hợp lệ');
      }
      return integrationService.importConfiguration(config);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: INTEGRATION_QUERY_KEYS.lists() });
    },
    onError: error => {
      console.error('Lỗi khi import cấu hình:', error);
    },
  });
};
