import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Card,
  Typography,
  Button,
  Icon,
  Input,
  Select,
  ResponsiveGrid,
  Modal,
  Badge,
} from '@/shared/components/common';
import { SmsProviderConfig, SmsProviderQueryParams, SmsProviderType, SmsProviderStatus } from '../types';
import { SMS_PROVIDER_TEMPLATES } from '../constants';
import SmsProviderCard from './SmsProviderCard';

interface SmsProviderListProps {
  /**
   * List of SMS providers
   */
  providers: SmsProviderConfig[];

  /**
   * Loading state
   */
  loading?: boolean;

  /**
   * Query parameters
   */
  queryParams?: SmsProviderQueryParams;

  /**
   * Callback when query parameters change
   */
  onQueryChange?: (params: SmsProviderQueryParams) => void;

  /**
   * Callback when add new provider button is clicked
   */
  onAddProvider?: () => void;

  /**
   * Callback when edit provider button is clicked
   */
  onEditProvider?: (provider: SmsProviderConfig) => void;

  /**
   * Callback when delete provider button is clicked
   */
  onDeleteProvider?: (provider: SmsProviderConfig) => void;

  /**
   * Callback when test provider button is clicked
   */
  onTestProvider?: (provider: SmsProviderConfig) => void;

  /**
   * Callback when toggle provider status button is clicked
   */
  onToggleProviderStatus?: (provider: SmsProviderConfig) => void;

  /**
   * Total count for pagination
   */
  totalCount?: number;

  /**
   * Current page
   */
  currentPage?: number;

  /**
   * Items per page
   */
  itemsPerPage?: number;

  /**
   * Callback when page changes
   */
  onPageChange?: (page: number) => void;
}

/**
 * Component hiển thị danh sách SMS Providers với tính năng tìm kiếm và lọc
 */
const SmsProviderList: React.FC<SmsProviderListProps> = ({
  providers,
  loading = false,
  queryParams = {},
  onQueryChange,
  onAddProvider,
  onEditProvider,
  onDeleteProvider,
  onTestProvider,
  onToggleProviderStatus,
  totalCount = 0,
  currentPage = 1,
  itemsPerPage = 10,
  onPageChange,
}) => {
  const { t } = useTranslation(['integration', 'common']);

  // Local state for search and filters
  const [searchTerm, setSearchTerm] = useState(queryParams.search || '');
  const [selectedStatus, setSelectedStatus] = useState(queryParams.status || '');
  const [selectedType, setSelectedType] = useState(queryParams.type || '');
  const [deleteModalOpen, setDeleteModalOpen] = useState(false);
  const [providerToDelete, setProviderToDelete] = useState<SmsProviderConfig | null>(null);

  // Filter options
  const statusOptions = [
    { value: '', label: t('integration:sms.allStatuses', 'Tất cả trạng thái') },
    { value: 'active', label: t('integration:sms.status.active', 'Hoạt động') },
    { value: 'inactive', label: t('integration:sms.status.inactive', 'Tạm dừng') },
    { value: 'error', label: t('integration:sms.status.error', 'Lỗi') },
    { value: 'testing', label: t('integration:sms.status.testing', 'Đang test') },
    { value: 'pending', label: t('integration:sms.status.pending', 'Chờ xử lý') },
  ];

  const typeOptions = [
    { value: '', label: t('integration:sms.allTypes', 'Tất cả loại') },
    ...Object.values(SMS_PROVIDER_TEMPLATES).map(template => ({
      value: template.type,
      label: template.displayName,
    })),
  ];

  // Handle search
  const handleSearch = (value: string) => {
    setSearchTerm(value);
    if (onQueryChange) {
      onQueryChange({
        ...queryParams,
        ...(value && { search: value }),
        page: 1, // Reset to first page when searching
      });
    }
  };

  // Handle status filter
  const handleStatusFilter = (value: string | number | string[] | number[]) => {
    const stringValue = value as string;
    setSelectedStatus(stringValue);
    if (onQueryChange) {
      onQueryChange({
        ...queryParams,
        ...(stringValue && { status: stringValue as SmsProviderStatus }),
        page: 1, // Reset to first page when filtering
      });
    }
  };

  // Handle type filter
  const handleTypeFilter = (value: string | number | string[] | number[]) => {
    const stringValue = value as string;
    setSelectedType(stringValue);
    if (onQueryChange) {
      onQueryChange({
        ...queryParams,
        type: (stringValue as SmsProviderType) || undefined,
        page: 1, // Reset to first page when filtering
      });
    }
  };

  // Handle delete confirmation
  const handleDeleteClick = (provider: SmsProviderConfig) => {
    setProviderToDelete(provider);
    setDeleteModalOpen(true);
  };

  const handleDeleteConfirm = () => {
    if (providerToDelete && onDeleteProvider) {
      onDeleteProvider(providerToDelete);
    }
    setDeleteModalOpen(false);
    setProviderToDelete(null);
  };

  const handleDeleteCancel = () => {
    setDeleteModalOpen(false);
    setProviderToDelete(null);
  };

  // Calculate pagination info
  const totalPages = Math.ceil(totalCount / itemsPerPage);
  const startItem = (currentPage - 1) * itemsPerPage + 1;
  const endItem = Math.min(currentPage * itemsPerPage, totalCount);

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <Typography variant="h4" className="font-bold">
            {t('integration:sms.providers', 'Nhà cung cấp SMS')}
          </Typography>
          <Typography variant="body2" className="text-muted-foreground">
            {t('integration:sms.providersDescription', 'Quản lý các nhà cung cấp dịch vụ SMS')}
          </Typography>
        </div>

        {onAddProvider && (
          <Button onClick={onAddProvider} variant="primary">
            <Icon name="plus" size="sm" className="mr-2" />
            {t('integration:sms.addProvider', 'Thêm nhà cung cấp')}
          </Button>
        )}
      </div>

      {/* Filters */}
      <Card className="p-4">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          {/* Search */}
          <div className="md:col-span-2">
            <Input
              type="text"
              placeholder={t('integration:sms.searchPlaceholder', 'Tìm kiếm theo tên...')}
              value={searchTerm}
              onChange={(e) => handleSearch(e.target.value)}
              leftIcon="search"
              fullWidth
            />
          </div>

          {/* Status Filter */}
          <div>
            <Select
              options={statusOptions}
              value={selectedStatus}
              onChange={handleStatusFilter}
              placeholder={t('integration:sms.filterByStatus', 'Lọc theo trạng thái')}
              fullWidth
            />
          </div>

          {/* Type Filter */}
          <div>
            <Select
              options={typeOptions}
              value={selectedType}
              onChange={handleTypeFilter}
              placeholder={t('integration:sms.filterByType', 'Lọc theo loại')}
              fullWidth
            />
          </div>
        </div>
      </Card>

      {/* Summary */}
      {totalCount > 0 && (
        <div className="flex items-center justify-between text-sm text-muted-foreground">
          <div>
            {t('integration:sms.showingResults', 'Hiển thị {{start}}-{{end}} trong tổng số {{total}} kết quả', {
              start: startItem,
              end: endItem,
              total: totalCount,
            })}
          </div>
          
          {/* Active providers count */}
          <div className="flex items-center space-x-4">
            <Badge variant="success" size="sm">
              {providers.filter(p => p.status === 'active').length} {t('integration:sms.active', 'hoạt động')}
            </Badge>
            <Badge variant="info" size="sm">
              {providers.filter(p => p.isDefault).length} {t('integration:sms.default', 'mặc định')}
            </Badge>
          </div>
        </div>
      )}

      {/* Providers Grid */}
      {loading ? (
        <div className="flex items-center justify-center py-12">
          <Icon name="loader" size="lg" className="animate-spin text-primary" />
          <Typography variant="body1" className="ml-3">
            {t('integration:sms.loading', 'Đang tải...')}
          </Typography>
        </div>
      ) : providers.length === 0 ? (
        <Card className="p-12 text-center">
          <Icon name="message-circle" size="xl" className="mx-auto text-muted-foreground mb-4" />
          <Typography variant="h6" className="mb-2">
            {searchTerm || selectedStatus || selectedType
              ? t('integration:sms.noResultsFound', 'Không tìm thấy kết quả')
              : t('integration:sms.noProviders', 'Chưa có nhà cung cấp nào')
            }
          </Typography>
          <Typography variant="body2" className="text-muted-foreground mb-6">
            {searchTerm || selectedStatus || selectedType
              ? t('integration:sms.tryDifferentFilters', 'Thử thay đổi bộ lọc hoặc từ khóa tìm kiếm')
              : t('integration:sms.addFirstProvider', 'Thêm nhà cung cấp SMS đầu tiên để bắt đầu')
            }
          </Typography>
          {onAddProvider && (
            <Button onClick={onAddProvider} variant="primary">
              <Icon name="plus" size="sm" className="mr-2" />
              {t('integration:sms.addProvider', 'Thêm nhà cung cấp')}
            </Button>
          )}
        </Card>
      ) : (
        <ResponsiveGrid
          maxColumns={{ xs: 1, sm: 1, md: 2, lg: 3 }}
          gap={6}
        >
          {providers.map((provider) => (
            <SmsProviderCard
              key={provider.id}
              provider={provider}
              {...(onEditProvider && { onEdit: onEditProvider })}
              onDelete={handleDeleteClick}
              {...(onTestProvider && { onTest: onTestProvider })}
              {...(onToggleProviderStatus && { onToggleStatus: onToggleProviderStatus })}
              loading={loading}
            />
          ))}
        </ResponsiveGrid>
      )}

      {/* Pagination */}
      {totalPages > 1 && onPageChange && (
        <div className="flex items-center justify-center space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => onPageChange(currentPage - 1)}
            disabled={currentPage <= 1}
          >
            <Icon name="chevron-left" size="sm" />
          </Button>

          {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
            <Button
              key={page}
              variant={page === currentPage ? 'primary' : 'outline'}
              size="sm"
              onClick={() => onPageChange(page)}
            >
              {page}
            </Button>
          ))}

          <Button
            variant="outline"
            size="sm"
            onClick={() => onPageChange(currentPage + 1)}
            disabled={currentPage >= totalPages}
          >
            <Icon name="chevron-right" size="sm" />
          </Button>
        </div>
      )}

      {/* Delete Confirmation Modal */}
      <Modal
        isOpen={deleteModalOpen}
        onClose={handleDeleteCancel}
        title={t('integration:sms.deleteProvider', 'Xóa nhà cung cấp')}
      >
        <div className="space-y-4">
          <Typography variant="body1">
            {t('integration:sms.deleteConfirmation', 'Bạn có chắc chắn muốn xóa nhà cung cấp "{{name}}"?', {
              name: providerToDelete?.displayName,
            })}
          </Typography>
          
          <Typography variant="body2" className="text-muted-foreground">
            {t('integration:sms.deleteWarning', 'Hành động này không thể hoàn tác. Tất cả cấu hình liên quan sẽ bị xóa.')}
          </Typography>

          <div className="flex items-center justify-end space-x-3 pt-4">
            <Button variant="outline" onClick={handleDeleteCancel}>
              {t('common:cancel', 'Hủy')}
            </Button>
            <Button variant="danger" onClick={handleDeleteConfirm}>
              <Icon name="trash-2" size="sm" className="mr-2" />
              {t('common:delete', 'Xóa')}
            </Button>
          </div>
        </div>
      </Modal>
    </div>
  );
};

export default SmsProviderList;
