import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';

import {
  <PERSON>ton,
  Card,
  Typography,
  Loading,
  Chip,
  Input,
  Textarea,
  FormItem,
} from '@/shared/components/common';
import { NotificationUtil } from '@/shared/utils/notification';
import { ToolDetail } from '../types/tool.types';
import { ToolStatus } from '../types/common.types';
import { UserToolVersion, EditUserToolVersionParams } from '../types/user-tool.types';
import { formatDate } from '@/shared/utils/format';
import { useUserToolVersionDetail } from '../hooks/useUserToolVersion';
import ToolVersionForm from './ToolVersionForm';

interface ToolFormProps {
  tool?: ToolDetail;
  isLoading?: boolean;
  onClose: () => void;
  onUpdateFromAdmin?: (toolId: string, adminVersionId: string) => void;
  onRollbackToAdmin?: (toolId: string, adminVersionId: string) => void;
  onSubmitVersion?: (versionId: string, values: EditUserToolVersionParams) => void;
}

/**
 * Component form xem chi tiết tool
 */
const ToolForm: React.FC<ToolFormProps> = ({
  tool,
  isLoading = false,
  onClose,
  onUpdateFromAdmin,
  onRollbackToAdmin,
  onSubmitVersion,
}) => {
  const { t } = useTranslation(['tools', 'common']);

  // State cho version management
  const [selectedVersionForEdit, setSelectedVersionForEdit] = useState<UserToolVersion | null>(
    null
  );
  const [showVersionForm, setShowVersionForm] = useState(false);

  // Hook để fetch chi tiết version khi được chọn
  const { data: versionDetail, isLoading: isLoadingVersionDetail } = useUserToolVersionDetail(
    tool?.id || '',
    selectedVersionForEdit?.id || ''
  );

  // Sử dụng danh sách phiên bản từ tool
  const versions = React.useMemo(() => tool?.versions || [], [tool]);

  // Cập nhật selectedVersionForEdit khi có dữ liệu chi tiết từ API
  useEffect(() => {
    if (versionDetail && selectedVersionForEdit) {
      setSelectedVersionForEdit(versionDetail);
    }
  }, [versionDetail, selectedVersionForEdit]);

  // Handlers cho version management
  const handleVersionClick = (e: React.MouseEvent, version: UserToolVersion) => {
    e.preventDefault();
    e.stopPropagation();
    // Set version để trigger API call qua useUserToolVersionDetail
    setSelectedVersionForEdit(version);
    setShowVersionForm(true);
  };

  const handleVersionFormSubmit = (values: EditUserToolVersionParams) => {
    // Sử dụng versionDetail (dữ liệu mới nhất từ API) hoặc fallback về selectedVersionForEdit
    const versionToUpdate = versionDetail || selectedVersionForEdit;
    if (versionToUpdate && onSubmitVersion) {
      onSubmitVersion(versionToUpdate.id, values);
      // Đóng form sau khi submit thành công
      setShowVersionForm(false);
      setSelectedVersionForEdit(null);
      NotificationUtil.success({
        message: t('tools:updateVersionSuccess', 'Phiên bản đã được cập nhật thành công!'),
        duration: 3000,
      });
    }
  };

  // Xác định variant cho status chip
  const getStatusVariant = (
    status: ToolStatus
  ): 'primary' | 'success' | 'warning' | 'danger' | 'info' => {
    switch (status) {
      case ToolStatus.APPROVED:
        return 'success';
      case ToolStatus.DRAFT:
        return 'warning';
      case ToolStatus.DEPRECATED:
        return 'danger';
      default:
        return 'primary';
    }
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <Loading size="lg" />
      </div>
    );
  }

  if (!tool) {
    return (
      <Card className="p-6">
        <Typography variant="h5" className="text-center text-red-500">
          {t('tools:notFound', 'Không tìm thấy công cụ')}
        </Typography>
        <div className="flex justify-center mt-4">
          <Button variant="primary" onClick={onClose}>
            {t('common:close', 'Đóng')}
          </Button>
        </div>
      </Card>
    );
  }

  return (
    <form className="space-y-6 p-4">
      {/* Header */}
      <div className="flex justify-between items-center">
        <Typography variant="h5" className="font-bold">
          {t('tools:toolDetails', 'Chi tiết Tool')}
        </Typography>
      </div>

      <div className="space-y-4">
        {/* Thông tin cơ bản */}
        <div className="space-y-4">
          <FormItem label={t('tools:name', 'Tool Name')} required>
            <Input value={tool.name} fullWidth disabled={true} readOnly={true} />
          </FormItem>

          <FormItem label={t('tools:description', 'Description')}>
            <Textarea value={tool.description || ''} disabled={true} readOnly={true} rows={3} />
          </FormItem>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <FormItem label={t('tools:status', 'Status')}>
              <div className="flex items-center">
                <Chip variant={getStatusVariant(tool.status)} size="sm">
                  {tool.status === ToolStatus.APPROVED
                    ? t('tools:status.approved', 'Đã duyệt')
                    : tool.status === ToolStatus.DRAFT
                      ? t('tools:status.draft', 'Bản nháp')
                      : t('tools:status.deprecated', 'Không dùng')}
                </Chip>
              </div>
            </FormItem>
          </div>
        </div>

        {/* Thông tin phiên bản */}
        <div className="space-y-6 pt-6   dark:border-gray-700">
          {/* Version Management Section */}
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <Typography variant="h6">{t('tools:versions', 'Phiên bản')}</Typography>
            </div>

            {/* Version Buttons - Hiển thị theo chiều ngang */}
            <div className="space-y-4">
              {versions.length > 0 ? (
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3">
                  {versions.map(version => (
                    <Card
                      key={version.id}
                      className={`p-3 cursor-pointer transition-all hover:shadow-md border max-w-xs ${
                        selectedVersionForEdit?.id === version.id
                          ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
                          : 'border-gray-200 dark:border-gray-700'
                      }`}
                      onClick={e => handleVersionClick(e, version)}
                    >
                      <div className="space-y-2">
                        <div className="flex justify-between items-start">
                          <Typography variant="subtitle2" className="font-semibold">
                            v{version.versionNumber}
                            {tool.defaultVersion?.id === version.id && (
                              <span className="ml-2 text-green-600 text-xs">
                                ({t('tools:default', 'Mặc định')})
                              </span>
                            )}
                          </Typography>
                        </div>
                        <Typography variant="body2" className="text-gray-600 line-clamp-2">
                          {version.toolName}
                        </Typography>
                        <Typography variant="caption" className="text-gray-500">
                          {formatDate(version.createdAt)}
                        </Typography>
                      </div>
                    </Card>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8 text-gray-500">
                  <Typography variant="body2">
                    {t('tools:noVersions', 'Chưa có phiên bản nào')}
                  </Typography>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Edit Version Form - Hiển thị khi có version được chọn */}
        {showVersionForm && selectedVersionForEdit && (
          <Card className="p-4 bg-gray-50 dark:bg-gray-800">
            <ToolVersionForm
              initialValues={selectedVersionForEdit}
              onSubmit={handleVersionFormSubmit}
              onCancel={() => {
                setShowVersionForm(false);
                setSelectedVersionForEdit(null);
              }}
              isEdit={true}
              isLoading={isLoadingVersionDetail}
            />
          </Card>
        )}

        {/* Action buttons for admin updates - Luôn hiển thị khi có handlers */}
        {(onUpdateFromAdmin || onRollbackToAdmin) && (
          <div className="pt-4 dark:border-gray-700">
            <div className="flex space-x-2">
              {onUpdateFromAdmin && (
                <Button
                  variant="primary"
                  onClick={() => onUpdateFromAdmin(tool.id, tool.originalId || '')}
                >
                  {t('tools:updateFromAdmin', 'Cập nhật từ Admin')}
                </Button>
              )}
              {onRollbackToAdmin && (
                <Button
                  variant="outline"
                  onClick={() => onRollbackToAdmin(tool.id, tool.originalId || '')}
                >
                  {t('tools:rollback', 'Khôi phục gốc')}
                </Button>
              )}
            </div>
          </div>
        )}
      </div>

      {/* Footer */}
      <div className="flex justify-end space-x-3 pt-6  dark:border-gray-700">
        <Button variant="outline" onClick={onClose}>
          {t('common:close', 'Đóng')}
        </Button>
      </div>
    </form>
  );
};

export default ToolForm;
