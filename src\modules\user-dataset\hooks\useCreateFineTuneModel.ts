import { useState } from 'react';
import { createFineTuneModel } from '../services/fine-tune-model.service';
import {
  CreateFineTuneModelDto,
  CreateFineTuneModelResponse,
} from '../user-data-fine-tune/types/fine-tune-model.types';

interface UseCreateFineTuneModelReturn {
  createModel: (data: CreateFineTuneModelDto) => Promise<CreateFineTuneModelResponse>;
  isLoading: boolean;
  error: string | null;
  reset: () => void;
}

/**
 * Hook để quản lý việc tạo fine-tune model
 */
export const useCreateFineTuneModel = (): UseCreateFineTuneModelReturn => {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const createModel = async (
    data: CreateFineTuneModelDto
  ): Promise<CreateFineTuneModelResponse> => {
    setIsLoading(true);
    setError(null);

    try {
      const result = await createFineTuneModel(data);
      return result;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Có lỗi xảy ra khi tạo model';
      setError(errorMessage);
      throw err;
    } finally {
      setIsLoading(false);
    }
  };

  const reset = () => {
    setError(null);
    setIsLoading(false);
  };

  return {
    createModel,
    isLoading,
    error,
    reset,
  };
};
