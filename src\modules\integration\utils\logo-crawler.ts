/**
 * Logo Crawler Utility
 * Crawl và download logo từ website URL
 */

export interface LogoInfo {
  blob: Blob;
  mimeType: string;
  url: string;
  size: number;
}

/**
 * Normalize URL để đảm bảo có protocol
 */
const normalizeUrl = (host: string): string => {
  let url = host.trim();

  // Thêm protocol nếu chưa có
  if (!url.startsWith('http://') && !url.startsWith('https://')) {
    url = `https://${url}`;
  }

  return url;
};

/**
 * Lấy favicon URL từ Google Favicon API
 */
const getGoogleFaviconUrl = (domain: string, size: number = 64): string => {
  return `https://www.google.com/s2/favicons?domain=${domain}&sz=${size}`;
};

/**
 * Thử multiple sizes từ Google Favicon API
 */
const tryGoogleFaviconSizes = async (domain: string): Promise<LogoInfo | null> => {
  const sizes = [128, 64, 32, 16]; // Thử từ size lớn xuống nhỏ

  for (const size of sizes) {
    const googleFaviconUrl = getGoogleFaviconUrl(domain, size);
    console.log(`Trying Google Favicon API with size ${size}: ${googleFaviconUrl}`);

    // Sử dụng timeout ngắn hơn cho Google API (5s)
    const logoInfo = await downloadAndValidateImage(googleFaviconUrl, 5000);
    if (logoInfo) {
      console.log(`Successfully got favicon from Google API with size ${size}`);
      return logoInfo;
    }
  }

  return null;
};

/**
 * Extract domain từ URL
 */
const extractDomain = (url: string): string => {
  try {
    const urlObj = new URL(url);
    return urlObj.hostname;
  } catch {
    // Nếu không parse được URL, assume đây là domain
    return url.replace(/^https?:\/\//, '').replace(/\/.*$/, '');
  }
};

/**
 * Lấy danh sách các URL có thể chứa favicon/logo (fallback)
 */
const getFallbackFaviconUrls = (baseUrl: string): string[] => {
  const url = new URL(baseUrl);
  const origin = url.origin;

  return [
    `${origin}/favicon.ico`,
    `${origin}/favicon.png`,
    `${origin}/apple-touch-icon.png`,
    `${origin}/apple-touch-icon-precomposed.png`,
    `${origin}/logo.png`,
    `${origin}/logo.svg`,
  ];
};

/**
 * Parse HTML để tìm favicon links
 */
const parseFaviconFromHtml = (html: string, baseUrl: string): string[] => {
  const faviconUrls: string[] = [];
  const url = new URL(baseUrl);
  const origin = url.origin;

  // Regex để tìm favicon links
  const linkRegex = /<link[^>]*rel=["'](?:icon|shortcut icon|apple-touch-icon)[^>]*href=["']([^"']+)["'][^>]*>/gi;

  let match;
  while ((match = linkRegex.exec(html)) !== null) {
    let href = match[1];

    // Convert relative URL to absolute
    if (href && href.startsWith('/')) {
      href = origin + href;
    } else if (href && !href.startsWith('http')) {
      href = origin + '/' + href;
    }

    if (href) {
      faviconUrls.push(href);
    }
  }

  return faviconUrls;
};

/**
 * Download và validate image với timeout
 */
const downloadAndValidateImage = async (url: string, timeout: number = 10000): Promise<LogoInfo | null> => {
  try {
    // Tạo AbortController để handle timeout
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), timeout);

    const response = await fetch(url, {
      method: 'GET',
      mode: 'cors',
      signal: controller.signal,
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
      },
    });

    clearTimeout(timeoutId);

    if (!response.ok) {
      return null;
    }

    const contentType = response.headers.get('content-type');
    if (!contentType || !contentType.startsWith('image/')) {
      return null;
    }

    const blob = await response.blob();

    // Validate file size (max 5MB)
    if (blob.size > 5 * 1024 * 1024) {
      return null;
    }

    // Validate minimum size (at least 1KB)
    if (blob.size < 1024) {
      return null;
    }

    return {
      blob,
      mimeType: contentType,
      url,
      size: blob.size,
    };
  } catch (error) {
    console.warn(`Failed to download image from ${url}:`, error);
    return null;
  }
};

/**
 * Crawl website để lấy HTML và parse favicon
 */
const crawlWebsiteHtml = async (baseUrl: string): Promise<string[]> => {
  try {
    const response = await fetch(baseUrl, {
      method: 'GET',
      mode: 'cors',
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
      },
    });

    if (!response.ok) {
      return [];
    }

    const html = await response.text();
    return parseFaviconFromHtml(html, baseUrl);
  } catch (error) {
    console.warn(`Failed to crawl website HTML from ${baseUrl}:`, error);
    return [];
  }
};

/**
 * Main function để crawl website logo
 */
export const crawlWebsiteLogo = async (host: string): Promise<LogoInfo | null> => {
  try {
    const baseUrl = normalizeUrl(host);
    const domain = extractDomain(host);

    // 1. Thử Google Favicon API trước (primary method)
    console.log(`Trying Google Favicon API for domain: ${domain}`);
    const googleLogoInfo = await tryGoogleFaviconSizes(domain);
    if (googleLogoInfo) {
      return googleLogoInfo;
    }

    // 2. Fallback: Thử crawl HTML để tìm favicon links
    console.log(`Google Favicon API failed, trying HTML crawling for: ${baseUrl}`);
    const htmlFaviconUrls = await crawlWebsiteHtml(baseUrl);

    // 3. Fallback: Lấy danh sách favicon URLs mặc định
    const fallbackFaviconUrls = getFallbackFaviconUrls(baseUrl);

    // 4. Combine và deduplicate URLs
    const allUrls = [...new Set([...htmlFaviconUrls, ...fallbackFaviconUrls])];

    // 5. Thử download từng URL cho đến khi thành công
    for (const url of allUrls) {
      const logoInfo = await downloadAndValidateImage(url);
      if (logoInfo) {
        console.log(`Successfully crawled logo from fallback: ${url}`);
        return logoInfo;
      }
    }

    console.warn(`No valid logo found for ${host} after trying all methods`);
    return null;
  } catch (error) {
    console.error(`Error crawling logo for ${host}:`, error);
    return null;
  }
};

/**
 * Upload logo lên presigned URL
 */
export const uploadLogoToPresignedUrl = async (
  blob: Blob,
  presignedUrl: string
): Promise<boolean> => {
  try {
    const response = await fetch(presignedUrl, {
      method: 'PUT',
      body: blob,
      headers: {
        'Content-Type': blob.type,
      },
    });

    if (!response.ok) {
      console.error('Failed to upload logo:', response.statusText);
      return false;
    }

    console.log('Logo uploaded successfully');
    return true;
  } catch (error) {
    console.error('Error uploading logo:', error);
    return false;
  }
};

/**
 * Detect MIME type từ file extension
 */
export const detectMimeTypeFromUrl = (url: string): string | null => {
  const extension = url.split('.').pop()?.toLowerCase();

  switch (extension) {
    case 'png':
      return 'image/png';
    case 'jpg':
    case 'jpeg':
      return 'image/jpeg';
    case 'gif':
      return 'image/gif';
    case 'svg':
      return 'image/svg+xml';
    case 'webp':
      return 'image/webp';
    case 'ico':
      return 'image/x-icon';
    default:
      return null; // Default fallback
  }
};

/**
 * Validate image blob
 */
export const validateImageBlob = (blob: Blob): boolean => {
  // Check MIME type
  if (!blob.type.startsWith('image/')) {
    return false;
  }

  // Check size (max 5MB, min 1KB)
  if (blob.size > 5 * 1024 * 1024 || blob.size < 1024) {
    return false;
  }

  return true;
};
