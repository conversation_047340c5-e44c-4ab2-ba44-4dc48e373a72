import React, { useState, useMemo } from 'react';
import { AlertProps } from './types';
import { Icon } from '@/shared/components/common';
import { IconName } from '@/shared/components/common/Icon/Icon';
import { useTheme } from '@/shared/contexts';
import { useTranslation } from 'react-i18next';

const Alert: React.FC<AlertProps> = ({
  type = 'info',
  title,
  message,
  description,
  showIcon = true,
  closable = false,
  onClose,
  icon,
  className = '',
  banner = false,
  action,
  ...rest
}) => {
  const [closed, setClosed] = useState(false);
  useTranslation(); // For i18n support
  useTheme();

  // Type classes
  const typeClasses = useMemo(() => {
    const baseClasses = {
      info: 'bg-blue-50 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300 border-blue-200 dark:border-blue-800/50',
      success:
        'bg-green-50 text-green-800 dark:bg-green-900/30 dark:text-green-300 border-green-200 dark:border-green-800/50',
      warning:
        'bg-yellow-50 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300 border-yellow-200 dark:border-yellow-800/50',
      error:
        'bg-red-50 text-red-800 dark:bg-red-900/30 dark:text-red-300 border-red-200 dark:border-red-800/50',
    };

    return baseClasses[type];
  }, [type]);

  // Icon name
  const iconName = useMemo(() => {
    if (icon) return icon as IconName;

    const defaultIcons: Record<string, IconName> = {
      info: 'info',
      success: 'check',
      warning: 'warning',
      error: 'close',
    };

    return defaultIcons[type] || 'info';
  }, [icon, type]);

  // Icon color
  const iconColor = useMemo(() => {
    const colors = {
      info: 'text-blue-500 dark:text-blue-400',
      success: 'text-green-500 dark:text-green-400',
      warning: 'text-yellow-500 dark:text-yellow-400',
      error: 'text-red-500 dark:text-red-400',
    };

    return colors[type];
  }, [type]);

  // Handle close
  const handleClose = () => {
    setClosed(true);
    onClose?.();
  };

  // Nếu đã đóng, không hiển thị gì
  if (closed) {
    return null;
  }

  // Border radius class
  const borderRadiusClass = banner ? 'rounded-none' : 'rounded-md';

  return (
    <div
      className={`p-4 ${typeClasses} ${borderRadiusClass} border ${className}`}
      role="alert"
      {...rest}
    >
      <div className="flex">
        {showIcon && (
          <div className={`flex-shrink-0 mr-3 ${iconColor}`}>
            <Icon name={iconName} size="md" />
          </div>
        )}

        <div className="flex-1 min-w-0">
          {title && <div className="text-lg font-medium mb-1">{title}</div>}

          <div className={`${description ? 'font-medium' : ''}`}>{message}</div>

          {description && <div className="mt-1 text-sm opacity-90">{description}</div>}
        </div>

        {action && <div className="flex-shrink-0 ml-3">{action}</div>}

        {closable && (
          <div className="flex-shrink-0 ml-3">
            <button
              type="button"
              className="inline-flex text-gray-400 hover:text-gray-500 focus:outline-none"
              onClick={handleClose}
              aria-label="Close"
            >
              <Icon name="close" size="sm" />
            </button>
          </div>
        )}
      </div>
    </div>
  );
};

export default Alert;
