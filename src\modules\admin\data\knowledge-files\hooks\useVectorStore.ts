import { useQueryClient } from '@tanstack/react-query';
import { useApiQuery, useApiMutation, useApiDeleteMutation } from '@/shared/api/hooks';
import { vectorStoreService } from '../services/vector-store.service';
import {
  ApiResponse,
  AssignFilesToVectorStoreDto,
  CreateVectorStoreDto,
  VectorStoreDto,
  VectorStoreFilesResponse,
  VectorStoreListResponse,
  VectorStoreQueryParams,
} from '../types';

// Định nghĩa các query key
export const VECTOR_STORE_QUERY_KEYS = {
  all: ['admin', 'vector-stores'] as const,
  lists: () => [...VECTOR_STORE_QUERY_KEYS.all, 'list'] as const,
  list: (filters: VectorStoreQueryParams) => [...VECTOR_STORE_QUERY_KEYS.lists(), filters] as const,
  details: () => [...VECTOR_STORE_QUERY_KEYS.all, 'detail'] as const,
  detail: (id: string) => [...VECTOR_STORE_QUERY_KEYS.details(), id] as const,
  files: (id: string) => [...VECTOR_STORE_QUERY_KEYS.detail(id), 'files'] as const,
};

/**
 * Hook để lấy danh sách vector store
 * @param params Tham số truy vấn
 * @returns Kết quả truy vấn danh sách vector store
 */
export const useVectorStores = (params?: VectorStoreQueryParams) => {
  return useApiQuery<VectorStoreListResponse>(
    VECTOR_STORE_QUERY_KEYS.list(params || {}),
    '/admin/vector-stores',
    {
      params,
    }
  );
};

/**
 * Hook để lấy thông tin chi tiết vector store
 * @param vectorStoreId ID của vector store
 * @returns Kết quả truy vấn thông tin chi tiết vector store
 */
export const useVectorStoreDetail = (vectorStoreId: string) => {
  return useApiQuery<VectorStoreDto>(
    VECTOR_STORE_QUERY_KEYS.detail(vectorStoreId),
    `/admin/vector-stores/${vectorStoreId}`,
    {},
    {
      enabled: !!vectorStoreId,
    }
  );
};

/**
 * Hook để tạo vector store mới
 * @returns Mutation để tạo vector store
 */
export const useCreateVectorStore = () => {
  const queryClient = useQueryClient();

  return useApiMutation<ApiResponse, CreateVectorStoreDto>('/admin/vector-stores', {
    onSuccess: () => {
      // Làm mới danh sách vector store sau khi tạo thành công
      queryClient.invalidateQueries({ queryKey: VECTOR_STORE_QUERY_KEYS.lists() });
    },
  });
};

/**
 * Hook để lấy danh sách file trong vector store
 * @param vectorStoreId ID của vector store
 * @param page Số trang
 * @param limit Số lượng kết quả trên một trang
 * @returns Kết quả truy vấn danh sách file trong vector store
 */
export const useVectorStoreFiles = (
  vectorStoreId: string | undefined,
  page: number = 1,
  limit: number = 10
) => {
  return useApiQuery<VectorStoreFilesResponse>(
    vectorStoreId ? VECTOR_STORE_QUERY_KEYS.files(vectorStoreId) : ['empty'],
    vectorStoreId ? `/admin/vector-stores/${vectorStoreId}/files` : '',
    {
      params: { page, limit },
    },
    {
      enabled: !!vectorStoreId,
    }
  );
};

/**
 * Hook để gỡ file khỏi vector store
 * @returns Mutation để gỡ file khỏi vector store
 */
export const useRemoveFileFromVectorStore = () => {
  const queryClient = useQueryClient();

  return useApiDeleteMutation<ApiResponse, { vectorStoreId: string; fileId: string }>(
    '/admin/vector-stores/:vectorStoreId/files/:fileId',
    {
      onSuccess: (_, variables) => {
        // Làm mới danh sách file trong vector store sau khi gỡ thành công
        queryClient.invalidateQueries({
          queryKey: VECTOR_STORE_QUERY_KEYS.files(variables.vectorStoreId),
        });
      },
    }
  );
};

/**
 * Hook để gỡ nhiều file khỏi vector store
 * @returns Object với phương thức removeMultipleFiles để gỡ nhiều file
 */
export const useRemoveMultipleFilesFromVectorStore = () => {
  const queryClient = useQueryClient();

  return {
    removeMultipleFiles: async (vectorStoreId: string, fileIds: string[]) => {
      try {
        const result = await vectorStoreService.removeMultipleFilesFromVectorStore(
          vectorStoreId,
          fileIds
        );

        // Làm mới danh sách file trong vector store sau khi gỡ thành công
        queryClient.invalidateQueries({
          queryKey: VECTOR_STORE_QUERY_KEYS.files(vectorStoreId),
        });

        return result;
      } catch (error) {
        console.error(
          '[useRemoveMultipleFilesFromVectorStore] Error removing multiple files:',
          error
        );
        throw error;
      }
    },
  };
};

/**
 * Hook để xóa một hoặc nhiều vector store
 * @returns Object với phương thức deleteStores để xóa vector store
 */
export const useDeleteVectorStore = () => {
  const queryClient = useQueryClient();

  return {
    deleteStores: async (storeIds: string | string[]) => {
      // Call service to delete vector stores
      const result = await vectorStoreService.deleteVectorStores(storeIds);

      // Refresh vector stores list after successful deletion
      queryClient.invalidateQueries({ queryKey: VECTOR_STORE_QUERY_KEYS.lists() });

      return result;
    },
  };
};

/**
 * Hook để gán file vào vector store
 * @returns Mutation để gán file vào vector store
 */
export const useAssignFilesToVectorStore = () => {
  const queryClient = useQueryClient();

  return {
    mutateAsync: async (variables: {
      vectorStoreId: string;
      data: AssignFilesToVectorStoreDto;
    }) => {
      try {
        const result = await vectorStoreService.assignFilesToVectorStore(
          variables.vectorStoreId,
          variables.data
        );

        // Làm mới danh sách file trong vector store sau khi gán thành công
        queryClient.invalidateQueries({
          queryKey: VECTOR_STORE_QUERY_KEYS.files(variables.vectorStoreId),
        });
        // Làm mới danh sách vector store để cập nhật thông tin
        queryClient.invalidateQueries({
          queryKey: VECTOR_STORE_QUERY_KEYS.lists(),
        });

        return result;
      } catch (error) {
        console.error('[useAssignFilesToVectorStore] Error assigning files:', error);
        throw error;
      }
    },
  };
};
