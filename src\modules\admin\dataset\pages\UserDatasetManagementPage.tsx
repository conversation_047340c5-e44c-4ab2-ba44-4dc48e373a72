import React from 'react';
import { useTranslation } from 'react-i18next';

import { ModuleCard } from '@/modules/components/card';
import ResponsiveGrid from '@/shared/components/common/ResponsiveGrid/ResponsiveGrid';

/**
 * Trang tổng quan quản lý User Dataset
 */
const UserDatasetManagementPage: React.FC = () => {
  const { t } = useTranslation('admin-dataset');

  return (
    <div>
      <ResponsiveGrid
        maxColumns={{ xs: 1, sm: 2, md: 2, lg: 3, xl: 3 }}
        maxColumnsWithChatPanel={{ xs: 1, sm: 1, md: 1, lg: 2, xl: 3 }}
        gap={6}
      >
        {/* Dataset Fine-tune Card */}
        <ModuleCard
          title={t('dataFineTune.title', 'Dataset Fine-tune')}
          description={t(
            'dataFineTune.description',
            'Quản lý dataset để huấn luyện và fine-tune các model AI.'
          )}
          icon="database"
          linkTo="/admin/dataset/data-fine-tune"
        />
      </ResponsiveGrid>
    </div>
  );
};

export default UserDatasetManagementPage;
