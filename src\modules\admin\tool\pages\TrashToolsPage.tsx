import React, { useState, useMemo, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { Typography, Table, Card } from '@/shared/components/common';
import { TableColumn, SortOrder } from '@/shared/components/common/Table/types';
import MenuIconBar, { ColumnVisibility } from '@/modules/components/menu-bar/MenuIconBar';
import { NotificationUtil } from '@/shared/utils/notification';
import { ActionMenu, ActionMenuItem } from '@/shared/components/common';

import { useDeletedAdminTools, useRollbackAdminTool } from '@/modules/admin/tool/hooks/useTool';
import { ToolListItem, ToolQueryParams, ToolSortBy } from '@/modules/admin/tool/types/tool.types';
import { SortDirection } from '@/shared/dto/request/query.dto';
import { formatDate } from '@/shared/utils/format';

/**
 * Trang quản lý Tool đã xóa mềm - Sử dụng DataTableWithActions
 */
const TrashToolsPage: React.FC<Record<string, never>> = () => {
  const { t } = useTranslation(['admin', 'common']);

  // State cho tìm kiếm và phân trang
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [searchTerm, setSearchTerm] = useState('');
  const [sortBy, setSortBy] = useState<ToolSortBy>(ToolSortBy.CREATED_AT);
  const [sortDirection, setSortDirection] = useState<SortDirection>(SortDirection.DESC);

  // State cho hiển thị cột
  const [visibleColumns, setVisibleColumns] = useState<ColumnVisibility[]>([
    { id: 'name', label: t('admin:tool.table.name', 'Tên Tool'), visible: true },
    { id: 'description', label: t('admin:tool.table.description', 'Mô tả'), visible: true },
    { id: 'status', label: t('admin:tool.table.status', 'Trạng thái'), visible: true },
    { id: 'deletedAt', label: t('admin:tool.table.deletedAt', 'Ngày xóa'), visible: true },
    { id: 'deletedBy', label: t('admin:tool.trash.deletedBy', 'Người xóa'), visible: false },
  ]);

  // API hooks
  const { mutateAsync: rollbackTool } = useRollbackAdminTool();

  // Xử lý khôi phục tool
  const handleRollback = useCallback(
    async (tool: ToolListItem) => {
      try {
        await rollbackTool(tool.id);
        NotificationUtil.success({
          message: t('admin:tool.trash.rollbackSuccess', 'Khôi phục tool thành công'),
          duration: 3000,
        });
      } catch (error) {
        console.error('Error rolling back tool:', error);
        NotificationUtil.error({
          message: t('admin:tool.trash.rollbackError', 'Lỗi khi khôi phục tool'),
          duration: 3000,
        });
      }
    },
    [rollbackTool, t]
  );

  // Định nghĩa các cột cho bảng
  const columns = useMemo<TableColumn<ToolListItem>[]>(
    () => [
      {
        key: 'name',
        title: t('admin:tool.trash.name', 'Tên Tool'),
        dataIndex: 'name',
        render: (_, record: ToolListItem) => (
          <div className="flex items-center">
            <div className="flex-1 min-w-0">
              <Typography variant="body2" className="font-medium truncate">
                {record.name}
              </Typography>
              <Typography variant="caption" className="text-gray-500 truncate">
                ID: {record.id}
              </Typography>
            </div>
          </div>
        ),
        sortable: true,
      },
      {
        key: 'description',
        title: t('admin:tool.trash.description1', 'Mô tả'),
        dataIndex: 'description',
        render: (value: unknown) => (
          <Typography variant="body2" className="truncate max-w-xs">
            {String(value || '-')}
          </Typography>
        ),
        sortable: true,
      },
      {
        key: 'status',
        title: t('admin:tool.trash.status', 'Trạng thái'),
        dataIndex: 'status',
        render: (value: unknown) => {
          const status = value as string;
          const statusColors = {
            DRAFT: 'bg-yellow-100 text-yellow-800',
            APPROVED: 'bg-green-100 text-green-800',
            DEPRECATED: 'bg-red-100 text-red-800',
          };
          return (
            <span
              className={`px-2 py-1 rounded-full text-xs font-medium ${statusColors[status as keyof typeof statusColors] || 'bg-gray-100 text-gray-800'}`}
            >
              {status || '-'}
            </span>
          );
        },
        sortable: true,
      },
      {
        key: 'deletedAt',
        title: t('admin:tool.trash.deletedAt', 'Ngày xóa'),
        dataIndex: 'deletedAt',
        render: (value: unknown) => {
          // Xử lý cả string và number timestamp
          const timestamp = typeof value === 'string' ? parseInt(value, 10) : (value as number);
          return <span>{formatDate(timestamp)}</span>;
        },
        sortable: true,
      },
      {
        key: 'deletedBy',
        title: t('admin:tool.trash.deletedBy', 'Người xóa'),
        dataIndex: 'deletedBy',
        render: (value: unknown) => {
          const deletedBy = value as { name: string | null; email: string | null };
          if (!deletedBy || (!deletedBy.name && !deletedBy.email)) {
            return <span>-</span>;
          }
          return (
            <div>
              <div className="font-medium">{deletedBy.name || 'N/A'}</div>
              {deletedBy.email && <div className="text-xs text-gray-500">{deletedBy.email}</div>}
            </div>
          );
        },
      },
      {
        key: 'actions',
        title: t('', ''),
        render: (_: unknown, record: ToolListItem) => {
          // Tạo danh sách các action items
          const actionItems: ActionMenuItem[] = [
            {
              id: 'rollback',
              label: t('admin:tool.trash.rollback', 'Khôi phục'),
              icon: 'refresh',
              onClick: () => handleRollback(record),
            },
          ];

          return (
            <ActionMenu
              items={actionItems}
              menuTooltip={t('common:moreActions', 'Thêm thao tác')}
              iconSize="sm"
              iconVariant="default"
              placement="bottom"
              menuWidth="180px"
              showAllInMenu={true}
              preferRight={true}
            />
          );
        },
      },
    ],
    [t, handleRollback]
  );

  // Tạo query params cho API
  const queryParams = useMemo(() => {
    const params: any = {
      page: currentPage,
      limit: itemsPerPage,
      sortDirection: sortDirection,
    };

    if (searchTerm) {
      params.search = searchTerm;
    }

    if (sortBy) {
      params.sortBy = sortBy;
    }

    return params;
  }, [currentPage, itemsPerPage, searchTerm, sortBy, sortDirection]);

  // API hooks
  const { data: toolsData, isLoading } = useDeletedAdminTools(queryParams);

  // Xử lý thay đổi trang
  const handlePageChange = useCallback(
    (page: number, newPageSize: number) => {
      setCurrentPage(page);
      if (newPageSize !== itemsPerPage) {
        setItemsPerPage(newPageSize);
        setCurrentPage(1); // Reset về trang 1 khi thay đổi số mục trên trang
      }
    },
    [itemsPerPage]
  );

  // Xử lý tìm kiếm
  const handleSearch = useCallback((term: string) => {
    setSearchTerm(term);
    setCurrentPage(1); // Reset về trang 1 khi tìm kiếm
  }, []);

  // Xử lý thay đổi sắp xếp
  const handleSortChange = useCallback((column: string | null, order: SortOrder) => {
    if (column && Object.values(ToolSortBy).includes(column as ToolSortBy)) {
      setSortBy(column as ToolSortBy);
    } else {
      setSortBy(ToolSortBy.CREATED_AT);
    }
    setSortDirection(order === 'asc' ? SortDirection.ASC : SortDirection.DESC);
  }, []);

  // Xử lý thay đổi hiển thị cột
  const handleColumnVisibilityChange = useCallback((columns: ColumnVisibility[]) => {
    setVisibleColumns(columns);
  }, []);

  return (
    <div>
      <div className="space-y-4">
        {/* MenuIconBar */}
        <MenuIconBar
          onSearch={handleSearch}
          items={[
            {
              id: 'all',
              label: t('common:all', 'Tất cả'),
              icon: 'list',
              onClick: () => '',
            },
          ]}
          onColumnVisibilityChange={handleColumnVisibilityChange}
          columns={visibleColumns}
          showDateFilter={false}
          showColumnFilter={true}
          // Không có onAdd prop để ẩn nút thêm mới cho trang trash
        />

        {/* Table */}
        <Card className="overflow-hidden">
          <Table<ToolListItem>
            columns={columns}
            data={toolsData?.items || []}
            rowKey="id"
            loading={isLoading}
            sortable={true}
            onSortChange={handleSortChange}
            defaultSort={{
              column: sortBy,
              order: sortDirection === SortDirection.ASC ? 'asc' : 'desc',
            }}
            pagination={{
              current: currentPage,
              pageSize: itemsPerPage,
              total: toolsData?.meta.totalItems || 0,
              onChange: handlePageChange,
              showSizeChanger: true,
              pageSizeOptions: [10, 20, 50, 100],
              showFirstLastButtons: true,
              showPageInfo: true,
            }}
          />
        </Card>
      </div>
    </div>
  );
};

export default TrashToolsPage;
