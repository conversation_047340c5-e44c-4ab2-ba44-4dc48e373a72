/* eslint-disable prefer-const */
import React, { useState, useCallback, useEffect } from 'react';
import {
  ImportedConversation,
  DatasetMessage,
} from '../user-data-fine-tune/types/user-data-fine-tune.types';
import ConversationSidebar from './ConversationSidebar';
import ChatPanelWithRoleLogic from './ChatPanelWithRoleLogic';
import { useTranslation } from 'react-i18next';

// Constants cho localStorage keys - Validation specific
const VALIDATION_STORAGE_KEYS = {
  CONVERSATIONS: 'validation_conversations',
  SELECTED_CONVERSATION_ID: 'validation_selected_conversation_id',
  SIDEBAR_OPEN: 'validation_sidebar_open',
};

interface ChatLayoutValidationProps {
  /**
   * Callback khi có thay đổi conversations
   */
  onConversationsChange?: (conversations: ImportedConversation[]) => void;
}

/**
 * Component layout cho Validation Data chat interface
 * Role Selection Logic:
 * - Message 1: SystemRole (bắt buộc)
 * - Message 2: UserRole (bắt buộc)
 * - Message 3+: UserRole/AssistantRole (cho chọn, không có SystemRole)
 */
const ChatLayoutValidation: React.FC<ChatLayoutValidationProps> = ({ onConversationsChange }) => {
  const { t } = useTranslation();

  // State - riêng cho Validation Data với localStorage
  const [conversations, setConversations] = useState<ImportedConversation[]>(() => {
    const saved = localStorage.getItem(VALIDATION_STORAGE_KEYS.CONVERSATIONS);
    if (saved) {
      try {
        const parsed = JSON.parse(saved);
        // Convert createdAt strings back to Date objects
        return parsed.map(
          (conv: Omit<ImportedConversation, 'createdAt'> & { createdAt: string }) => ({
            ...conv,
            createdAt: new Date(conv.createdAt),
          })
        );
      } catch (error) {
        console.error('Error parsing validation conversations from localStorage:', error);
        return [];
      }
    }
    return [];
  });

  const [selectedConversationId, setSelectedConversationId] = useState<string | null>(() => {
    return localStorage.getItem(VALIDATION_STORAGE_KEYS.SELECTED_CONVERSATION_ID) || null;
  });

  const [isSidebarOpen, setIsSidebarOpen] = useState(() => {
    const saved = localStorage.getItem(VALIDATION_STORAGE_KEYS.SIDEBAR_OPEN);
    return saved ? JSON.parse(saved) : true;
  });

  // Save to localStorage khi state thay đổi
  useEffect(() => {
    localStorage.setItem(VALIDATION_STORAGE_KEYS.CONVERSATIONS, JSON.stringify(conversations));
    // Notify parent component về thay đổi
    onConversationsChange?.(conversations);
  }, [conversations, onConversationsChange]);

  useEffect(() => {
    if (selectedConversationId) {
      localStorage.setItem(
        VALIDATION_STORAGE_KEYS.SELECTED_CONVERSATION_ID,
        selectedConversationId
      );
    } else {
      localStorage.removeItem(VALIDATION_STORAGE_KEYS.SELECTED_CONVERSATION_ID);
    }
  }, [selectedConversationId]);

  useEffect(() => {
    localStorage.setItem(VALIDATION_STORAGE_KEYS.SIDEBAR_OPEN, JSON.stringify(isSidebarOpen));
  }, [isSidebarOpen]);

  // Get selected conversation
  const selectedConversation = conversations.find(conv => conv.id === selectedConversationId);

  // Generate unique ID
  const generateId = (): string => {
    return Date.now().toString(36) + Math.random().toString(36).substring(2);
  };

  // Generate conversation title from first user message
  const generateTitle = (messages: DatasetMessage[]): string => {
    const firstUserMessage = messages.find(msg => msg.role === 'user');
    if (firstUserMessage) {
      // Extract text content for title
      let textContent = '';
      if (typeof firstUserMessage.content === 'string') {
        textContent = firstUserMessage.content;
      } else {
        const textItem = firstUserMessage.content.find(item => item.type === 'text');
        textContent = textItem ? textItem.text : 'Image message';
      }

      // Lấy 30 ký tự đầu để title ngắn gọn hơn
      return textContent.length > 30 ? textContent.substring(0, 30) + '...' : textContent;
    }
    return 'Validation Conversation';
  };

  // Handle import file
  const handleImportFile = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = e => {
      try {
        if (!e.target) {
          console.error('[Validation] Error reading file: Target is undefined');
          return;
        }

        const content = e.target.result as string;
        if (!content) {
          console.error('[Validation] Error reading file: Content is undefined');
          return;
        }

        const isJsonl = file.name.toLowerCase().endsWith('.jsonl');

        if (isJsonl) {
          // Parse JSONL format
          const lines = content.trim().split('\n');

          const newConversations: ImportedConversation[] = [];

          lines.forEach((line, index) => {
            try {
              const trimmedLine = line.trim();
              if (trimmedLine) {
                const jsonObj = JSON.parse(trimmedLine);
                if (jsonObj && jsonObj.messages && Array.isArray(jsonObj.messages)) {
                  const conversation: ImportedConversation = {
                    id: generateId(),
                    title: generateTitle(jsonObj.messages),
                    messages: jsonObj.messages,
                    createdAt: new Date(),
                  };
                  newConversations.push(conversation);
                } else {
                  console.warn(`⚠️ [Validation] Line ${index + 1} missing messages array`);
                }
              }
            } catch (lineError) {
              console.error(`❌ [Validation] Error parsing line ${index + 1}:`, lineError);
            }
          });

          if (newConversations.length > 0) {
            setConversations(prev => [...prev, ...newConversations]);
            // Auto select first imported conversation
            const firstConversation = newConversations[0];
            if (firstConversation) {
              setSelectedConversationId(firstConversation.id);
            }
          } else {
            console.warn('⚠️ [Validation] No conversations were imported');
          }
        } else {
          // Parse JSON format (legacy)
          const json = JSON.parse(content);
          if (json && json.conversations && Array.isArray(json.conversations)) {
            const newConversations: ImportedConversation[] = json.conversations.map(
              (conv: { messages: DatasetMessage[] }) => ({
                id: generateId(),
                title: generateTitle(conv.messages),
                messages: conv.messages,
                createdAt: new Date(),
              })
            );

            setConversations(prev => [...prev, ...newConversations]);
            if (newConversations.length > 0) {
              const firstConversation = newConversations[0];
              if (firstConversation) {
                setSelectedConversationId(firstConversation.id);
              }
            }
          }
        }
      } catch (error) {
        console.error('[Validation] Error parsing file:', error);
        alert('Error parsing file. Please check the file format.');
      }
    };

    reader.readAsText(file);
    // Reset input
    event.target.value = '';
  }, []);

  // Handle select conversation
  const handleSelectConversation = useCallback((conversationId: string) => {
    setSelectedConversationId(conversationId);
  }, []);

  // Handle delete conversation
  const handleDeleteConversation = useCallback(
    (conversationId: string) => {
      setConversations(prev => {
        const updated = prev.filter(conv => conv.id !== conversationId);

        // If deleted conversation was selected, select another one
        if (selectedConversationId === conversationId) {
          const firstConversation = updated[0];
          setSelectedConversationId(firstConversation?.id || null);
        }

        return updated;
      });
    },
    [selectedConversationId]
  );

  // Handle toggle sidebar
  const handleToggleSidebar = useCallback(() => {
    setIsSidebarOpen((prev: boolean) => !prev);
  }, []);

  // Handle new chat
  const handleNewChat = useCallback(() => {
    const newConversation: ImportedConversation = {
      id: generateId(),
      title: t('admin-dataset:chatLayout.validation.defaultChatTitle'),
      messages: [],
      createdAt: new Date(),
    };

    setConversations(prev => [newConversation, ...prev]);
    setSelectedConversationId(newConversation.id);
  }, []);

  // Handle add message to selected conversation with role validation
  const handleAddMessage = useCallback(
    (message: DatasetMessage) => {
      if (!selectedConversationId) return;

      setConversations(prev => {
        const updated = prev.map(conv => {
          if (conv.id === selectedConversationId) {
            const currentMessages = conv.messages;
            const messageIndex = currentMessages.length;

            // Role validation logic
            let validatedMessage = { ...message };

            if (messageIndex === 0) {
              // Message 1: Must be SystemRole
              validatedMessage.role = 'system';
            } else if (messageIndex === 1) {
              // Message 2: Must be UserRole
              validatedMessage.role = 'user';
            }
            // Message 3+: Allow user/assistant (already validated in ChatPanel)

            const newMessages = [...currentMessages, validatedMessage];

            // Update title if this is first user message
            let newTitle = conv.title;
            if (messageIndex === 1 && validatedMessage.role === 'user') {
              newTitle = generateTitle([validatedMessage]);
            }

            return {
              ...conv,
              title: newTitle,
              messages: newMessages,
            };
          }
          return conv;
        });

        return updated;
      });
    },
    [selectedConversationId]
  );

  // Handle delete message from selected conversation
  const handleDeleteMessage = useCallback(
    (messageIndex: number) => {
      if (!selectedConversationId) return;

      setConversations(prev => {
        const updated = prev.map(conv => {
          if (conv.id === selectedConversationId) {
            const newMessages = [...conv.messages];
            newMessages.splice(messageIndex, 1);
            return {
              ...conv,
              messages: newMessages,
            };
          }
          return conv;
        });

        return updated;
      });
    },
    [selectedConversationId]
  );

  // Handle edit message in selected conversation
  const handleEditMessage = useCallback(
    (messageIndex: number, message: DatasetMessage) => {
      if (!selectedConversationId) return;

      setConversations(prev => {
        const updated = prev.map(conv => {
          if (conv.id === selectedConversationId) {
            const newMessages = [...conv.messages];

            // Role validation for editing
            let validatedMessage = { ...message };

            if (messageIndex === 0) {
              // Message 1: Must be SystemRole
              validatedMessage.role = 'system';
            } else if (messageIndex === 1) {
              // Message 2: Must be UserRole
              validatedMessage.role = 'user';
            }
            // Message 3+: Allow user/assistant (already validated in ChatPanel)

            newMessages[messageIndex] = validatedMessage;
            return {
              ...conv,
              messages: newMessages,
            };
          }
          return conv;
        });

        return updated;
      });
    },
    [selectedConversationId]
  );

  return (
    <div className="h-full flex relative">
      {/* Sidebar */}
      <div
        className={`transition-all duration-300 flex-shrink-0 ${isSidebarOpen ? 'w-80' : 'w-0'}`}
      >
        <ConversationSidebar
          conversations={conversations}
          selectedConversationId={selectedConversationId}
          onSelectConversation={handleSelectConversation}
          onDeleteConversation={handleDeleteConversation}
          onImportFile={handleImportFile}
          onNewChat={handleNewChat}
          isOpen={isSidebarOpen}
          onToggleSidebar={handleToggleSidebar}
        />
      </div>

      {/* Main Content */}
      <div className="flex-1 min-w-0 h-full">
        {selectedConversation ? (
          <div className="h-full w-full">
            <ChatPanelWithRoleLogic
              title={selectedConversation.title}
              messages={selectedConversation.messages}
              onAddMessage={handleAddMessage}
              onDeleteMessage={handleDeleteMessage}
              onEditMessage={handleEditMessage}
              placeholder={t('admin-dataset:chatLayout.validation.placeholder')}
            />
          </div>
        ) : (
          <div className="h-full flex items-center justify-center">
            <div className="text-center">
              <h3 className="text-lg font-semibold mb-2 text-gray-900 dark:text-white">
                {t('admin-dataset:chatLayout.validation.title')}
              </h3>
              <p className="text-gray-500 dark:text-gray-400 text-sm max-w-sm">
                {t('admin-dataset:chatLayout.validation.description')}
              </p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default ChatLayoutValidation;
