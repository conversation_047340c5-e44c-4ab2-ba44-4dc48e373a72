/* eslint-disable @typescript-eslint/no-explicit-any */
import React, { useState, useEffect, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import { Button, Input, FormItem, Textarea, Card } from '@/shared/components/common';
import { FileText, BarChart3 } from 'lucide-react';
import { NotificationUtil } from '@/shared/utils/notification';
// Removed React Hook Form - using simple useState instead
import DatasetForm from '../components/DatasetForm';
import ValidationDataForm from '../components/ValidationDataForm';

import { useCreateAndUploadDataset } from '../user-data-fine-tune/hooks/useUserDataFineTune';
import {
  ProviderFineTuneEnum,
  ImportedConversation,
} from '../user-data-fine-tune/types/user-data-fine-tune.types';
import { convertConversationsToMessagesArray } from '../types/jsonl.types';

// Constants cho localStorage keys
const STORAGE_KEYS = {
  TRAINING_CONVERSATIONS: 'openai_training_conversations',
  VALIDATION_CONVERSATIONS: 'openai_validation_conversations',
  ACTIVE_TAB: 'openai_active_tab',
};

// Simple form data interface
interface UnifiedDatasetFormData {
  name: string;
  description: string;
}

/**
 * Page để tạo dataset cho OpenAI
 * Có 2 tabs: Training Data và Validation Data
 * Tạo 1 nút chung để gộp cả training và validation data
 * Lưu conversations vào localStorage để không mất khi reload
 */
const CreateDatasetOpenAIPage: React.FC = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const { createAndUpload } = useCreateAndUploadDataset();

  // State cho tab selection với localStorage
  const [activeTab, setActiveTab] = useState<'training' | 'validation'>(() => {
    const saved = localStorage.getItem(STORAGE_KEYS.ACTIVE_TAB);
    return (saved as 'training' | 'validation') || 'training';
  });

  // State để control unified form hiển thị
  const [showUnifiedForm, setShowUnifiedForm] = useState(false);

  // State cho conversations với localStorage
  const [trainingConversations, setTrainingConversations] = useState<ImportedConversation[]>(() => {
    const saved = localStorage.getItem(STORAGE_KEYS.TRAINING_CONVERSATIONS);
    return saved ? JSON.parse(saved) : [];
  });

  const [validationConversations, setValidationConversations] = useState<ImportedConversation[]>(
    () => {
      const saved = localStorage.getItem(STORAGE_KEYS.VALIDATION_CONVERSATIONS);
      return saved ? JSON.parse(saved) : [];
    }
  );

  // Simple form state với useState
  const [formData, setFormData] = useState<UnifiedDatasetFormData>({
    name: '',
    description: '',
  });
  const [formErrors, setFormErrors] = useState<Partial<UnifiedDatasetFormData>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Save to localStorage khi conversations thay đổi
  useEffect(() => {
    localStorage.setItem(
      STORAGE_KEYS.TRAINING_CONVERSATIONS,
      JSON.stringify(trainingConversations)
    );
  }, [trainingConversations]);

  useEffect(() => {
    localStorage.setItem(
      STORAGE_KEYS.VALIDATION_CONVERSATIONS,
      JSON.stringify(validationConversations)
    );
  }, [validationConversations]);

  useEffect(() => {
    localStorage.setItem(STORAGE_KEYS.ACTIVE_TAB, activeTab);
  }, [activeTab]);

  // Handle conversations change
  const handleTrainingConversationsChange = useCallback((conversations: ImportedConversation[]) => {
    setTrainingConversations(conversations);
  }, []);

  const handleValidationConversationsChange = useCallback(
    (conversations: ImportedConversation[]) => {
      setValidationConversations(conversations);
    },
    []
  );

  // Clear localStorage
  const clearStorage = useCallback(() => {
    localStorage.removeItem(STORAGE_KEYS.TRAINING_CONVERSATIONS);
    localStorage.removeItem(STORAGE_KEYS.VALIDATION_CONVERSATIONS);
    localStorage.removeItem(STORAGE_KEYS.ACTIVE_TAB);
  }, []);

  // Simple form validation
  const validateForm = (): boolean => {
    const errors: Partial<UnifiedDatasetFormData> = {};

    if (!formData.name.trim()) {
      errors.name = 'Dataset name is required';
    }

    if (!formData.description.trim()) {
      errors.description = 'Dataset description is required';
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  // Xử lý submit form tạo dataset
  const handleFormSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Validate form
    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);

    try {
      // Convert conversations to Messages array format
      const trainDataArray = convertConversationsToMessagesArray(trainingConversations);
      const validDataArray =
        validationConversations.length > 0
          ? convertConversationsToMessagesArray(validationConversations)
          : [];

      // Convert arrays to JSONL strings
      const trainJsonlString = trainDataArray.map(item => JSON.stringify(item)).join('\n');

      const validJsonlString =
        validDataArray.length > 0
          ? validDataArray.map(item => JSON.stringify(item)).join('\n')
          : '';

      // Create File objects with application/jsonl type
      const trainFile = new File([trainJsonlString], 'train_data.jsonl', {
        type: 'application/jsonl',
      });

      const validFile = validJsonlString
        ? new File([validJsonlString], 'valid_data.jsonl', {
            type: 'application/jsonl',
          })
        : undefined;

      // Create dataset info for OpenAI - Send file.type string, not File object
      const datasetInfo: any = {
        name: formData.name,
        description: formData.description,
        provider: ProviderFineTuneEnum.OPENAI,
        trainDataset: trainFile.type, // ✅ Send "application/jsonl" string
        validDataset: validFile ? validFile.type : undefined, // ✅ Send "application/jsonl" string or undefined
      };

      // Create and upload dataset with JSONL strings
      await createAndUpload({
        datasetInfo,
        trainJsonlData: trainJsonlString, // ✅ Send JSONL string content
        validJsonlData: validJsonlString, // ✅ Send JSONL string content or undefined
      });

      // Show success notification
      NotificationUtil.success({
        title: t('user-dataset:createDataset.notification.success.title', 'Thành công'),
        message: t(
          'user-dataset:createDataset.notification.success.message',
          'Dataset OpenAI đã được tạo thành công!'
        ),
        duration: 5000,
      });

      // Reset form và clear data
      setFormData({ name: '', description: '' });
      setFormErrors({});
      clearStorage();
      setShowUnifiedForm(false);

      // Navigate back to dataset list
      navigate('/user-dataset/data-fine-tune');
    } catch (err) {
      console.error('Error creating OpenAI dataset:', err);

      // Show error notification
      NotificationUtil.error({
        title: t('user-dataset:createDataset.notification.error.title', 'Lỗi'),
        message: t(
          'user-dataset:createDataset.notification.error.message',
          'Không thể tạo dataset. Vui lòng thử lại.'
        ),
        duration: 5000,
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Get current conversation counts from state
  const currentTrainingCount = trainingConversations.length;
  const currentValidationCount = validationConversations.length;

  return (
    <div className="h-screen flex flex-col overflow-hidden">
      {/* Unified Form Modal */}
      {showUnifiedForm && (
        <Card>
          <form
            onSubmit={handleFormSubmit}
            onSubmitCapture={() =>
              console.log('🔥 [CreateDatasetOpenAIPage] Form submit captured!')
            }
          >
            <div className="grid grid-cols-1 gap-4 mb-4">
              <FormItem
                name="name"
                label={t('user-dataset:createDataset.formOpenAI.name')}
                helpText={formErrors.name}
                required
              >
                <Input
                  value={formData.name}
                  onChange={e => setFormData(prev => ({ ...prev, name: e.target.value }))}
                  placeholder={t('user-dataset:createDataset.formOpenAI.placeholderName')}
                  error={formErrors.name || ''}
                  fullWidth
                />
              </FormItem>

              <FormItem
                name="description"
                label={t('user-dataset:createDataset.formOpenAI.description')}
                helpText={formErrors.description}
                required
              >
                <Textarea
                  value={formData.description}
                  onChange={e => setFormData(prev => ({ ...prev, description: e.target.value }))}
                  placeholder={t('user-dataset:createDataset.formOpenAI.placeholderDescription')}
                  status={formErrors.description ? 'error' : 'default'}
                  rows={3}
                  fullWidth
                />
              </FormItem>
            </div>

            <div className="flex justify-end items-center">
              <div className="flex space-x-2">
                <Button type="button" variant="outline" onClick={() => setShowUnifiedForm(false)}>
                  {t('user-dataset:createDataset.action.cancel')}
                </Button>
                <Button
                  type="submit"
                  isLoading={isSubmitting}
                  disabled={currentTrainingCount === 0 || currentValidationCount === 0}
                  onClick={() => console.log('🔥 [CreateDatasetOpenAIPage] Submit button clicked!')}
                >
                  {t('user-dataset:createDataset.action.createDataset')}
                </Button>
              </div>
            </div>
          </form>
        </Card>
      )}

      {/* Tab Navigation */}
      <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-900 flex-shrink-0">
        <div className="flex space-x-1 bg-gray-100 dark:bg-gray-800 p-1 rounded-lg">
          <Button
            variant={activeTab === 'training' ? 'primary' : 'ghost'}
            size="sm"
            onClick={() => setActiveTab('training')}
            className="flex items-center"
          >
            <FileText size={16} className="mr-2" />
            {t('user-dataset:chatLayout.training.title')}
          </Button>
          <Button
            variant={activeTab === 'validation' ? 'primary' : 'ghost'}
            size="sm"
            onClick={() => setActiveTab('validation')}
            className="flex items-center"
          >
            <BarChart3 size={16} className="mr-2" />
            {t('user-dataset:chatLayout.validation.title')}
          </Button>
        </div>

        <div className="text-sm text-gray-600 dark:text-gray-400">
          {activeTab === 'training'}
          {activeTab === 'validation'}
          {/* Nút Tạo Dataset chính */}
          {(currentTrainingCount > 0 || currentValidationCount > 0) && (
            <Button onClick={() => setShowUnifiedForm(true)} size="sm">
              {t('user-dataset:createDataset.action.createDataset')} (
              {currentTrainingCount + currentValidationCount})
            </Button>
          )}
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 min-h-0 overflow-hidden">
        {activeTab === 'training' && (
          <DatasetForm onConversationsChange={handleTrainingConversationsChange} />
        )}

        {activeTab === 'validation' && (
          <ValidationDataForm onConversationsChange={handleValidationConversationsChange} />
        )}
      </div>
    </div>
  );
};

export default CreateDatasetOpenAIPage;
