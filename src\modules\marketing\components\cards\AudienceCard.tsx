import React from 'react';
import { Card, IconCard, Tooltip, Icon, Typography } from '@/shared/components/common';
import { Audience, AudienceStatus, AudienceType } from '../../types/audience.types';
import { useTranslation } from 'react-i18next';

interface AudienceCardProps {
  /**
   * Dữ liệu đối tượng
   */
  audience: Audience;

  /**
   * Callback khi click vào nút xem
   */
  onView?: (id: number) => void;

  /**
   * Callback khi click vào nút sửa
   */
  onEdit?: (id: number) => void;

  /**
   * Callback khi click vào nút xóa
   */
  onDelete?: (id: number) => void;
}

/**
 * Component hiển thị thông tin đối tượng dưới dạng card
 */
const AudienceCard: React.FC<AudienceCardProps> = ({ audience, onView, onEdit, onDelete }) => {
  const { t } = useTranslation('marketing');

  // <PERSON><PERSON><PERSON> định màu sắc dựa trên trạng thái
  const getStatusColor = (status: AudienceStatus) => {
    switch (status) {
      case AudienceStatus.ACTIVE:
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
      case AudienceStatus.DRAFT:
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';
      case AudienceStatus.INACTIVE:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200';
    }
  };

  // Xác định icon dựa trên loại đối tượng
  const getTypeIcon = (type: AudienceType) => {
    switch (type) {
      case AudienceType.CUSTOMER:
        return 'user';
      case AudienceType.LEAD:
        return 'user-plus';
      case AudienceType.SUBSCRIBER:
        return 'mail';
      case AudienceType.CUSTOM:
        return 'settings';
      default:
        return 'user';
    }
  };

  // Xác định tên loại đối tượng
  const getTypeName = (type: AudienceType) => {
    switch (type) {
      case AudienceType.CUSTOMER:
        return t('audience.type.customer', 'Khách hàng');
      case AudienceType.LEAD:
        return t('audience.type.lead', 'Tiềm năng');
      case AudienceType.SUBSCRIBER:
        return t('audience.type.subscriber', 'Người đăng ký');
      case AudienceType.CUSTOM:
        return t('audience.type.custom', 'Tùy chỉnh');
      default:
        return '';
    }
  };

  // Xác định tên trạng thái
  const getStatusName = (status: AudienceStatus) => {
    switch (status) {
      case AudienceStatus.ACTIVE:
        return t('common.active', 'Hoạt động');
      case AudienceStatus.DRAFT:
        return t('common.draft', 'Bản nháp');
      case AudienceStatus.INACTIVE:
        return t('common.inactive', 'Không hoạt động');
      default:
        return '';
    }
  };

  return (
    <Card hoverable variant="bordered" className="h-full">
      <div className="flex flex-col h-full">
        {/* Header */}
        <div className="flex items-center justify-between mb-3">
          <div className="flex items-center">
            <div className="p-2 rounded-full bg-primary/10 mr-3">
              <Icon name={getTypeIcon(audience.type)} size="md" className="text-primary" />
            </div>
            <div>
              <Typography variant="subtitle1" className="font-semibold line-clamp-1">
                {audience.name}
              </Typography>
              <Typography variant="caption" className="text-muted">
                {getTypeName(audience.type)}
              </Typography>
            </div>
          </div>
          <div
            className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(audience.status)}`}
          >
            {getStatusName(audience.status)}
          </div>
        </div>

        {/* Content */}
        <div className="flex-grow mb-4">
          <Typography variant="body2" className="text-muted line-clamp-2 mb-3">
            {t('audience.noDescription', 'Không có mô tả')}
          </Typography>

          <div className="flex items-center justify-between mt-2">
            <div>
              <Typography variant="caption" className="text-muted">
                {t('audience.contacts', 'Số liên hệ')}
              </Typography>
              <Typography variant="h4" className="font-bold text-primary">
                {audience.totalContacts}
              </Typography>
            </div>
            <div>
              <Typography variant="caption" className="text-muted">
                {t('audience.createdAt', 'Ngày tạo')}
              </Typography>
              <Typography variant="body2">{audience.createdAt}</Typography>
            </div>
          </div>
        </div>

        {/* Actions */}
        <div className="flex justify-end space-x-2 mt-auto pt-2 border-t border-gray-100 dark:border-gray-800">
          <Tooltip content={t('common.view', 'Xem')} position="top">
            <IconCard
              icon="eye"
              variant="default"
              size="sm"
              onClick={() => onView && onView(audience.id)}
            />
          </Tooltip>
          <Tooltip content={t('common.edit', 'Chỉnh sửa')} position="top">
            <IconCard
              icon="edit"
              variant="default"
              size="sm"
              onClick={() => onEdit && onEdit(audience.id)}
            />
          </Tooltip>
          <Tooltip content={t('common.delete', 'Xóa')} position="top">
            <IconCard
              icon="trash"
              variant="default"
              size="sm"
              onClick={() => onDelete && onDelete(audience.id)}
            />
          </Tooltip>
        </div>
      </div>
    </Card>
  );
};

export default AudienceCard;
