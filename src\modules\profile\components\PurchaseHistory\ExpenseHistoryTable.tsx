import React, { useState, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { Table, TableColumn } from '@/shared/components/common/Table';
import { Icon, Tooltip, IconCard, SearchBar, Checkbox } from '@/shared/components/common';
import { ModernMenu } from '@/shared/components/common';

// Interface cho cài đặt hiển thị cột
interface ColumnVisibility {
  id: string;
  label: string;
  visible: boolean;
}
import rpointImage from '@/shared/assets/images/rpoint.png';

// Enum cho các kênh mạng xã hội
enum SocialChannel {
  FACEBOOK = 'facebook',
  LINKEDIN = 'linkedin',
  INSTAGRAM = 'instagram',
  TIKTOK = 'tiktok',
  ZALO = 'zalo',
}

// Interface cho tham số truy vấn
interface ExpenseHistoryQueryParams {
  page: number;
  pageSize: number;
  search?: string;
  startDate?: string;
  endDate?: string;
  channel?: SocialChannel;
}

/**
 * Component hiển thị bảng lịch sử chi tiêu
 */
const ExpenseHistoryTable: React.FC = () => {
  const { t } = useTranslation('profile');
  const [searchText, setSearchText] = useState('');

  const [queryParams, setQueryParams] = useState<ExpenseHistoryQueryParams>({
    page: 1,
    pageSize: 5,
  });

  const [showSearch, setShowSearch] = useState(false);
  const [showFilterMenu, setShowFilterMenu] = useState(false);
  const [showColumnMenu, setShowColumnMenu] = useState(false);
  const [columnSettings, setColumnSettings] = useState<ColumnVisibility[]>([
    { id: 'all', label: t('common:selectAll', 'Chọn tất cả'), visible: true },
    { id: 'id', label: 'ID', visible: true },
    { id: 'time', label: t('expenseHistory.time', 'Thời gian'), visible: true },
    { id: 'points', label: t('expenseHistory.points', 'Số point'), visible: true },
    { id: 'channel', label: t('expenseHistory.channel', 'Kênh'), visible: true },
    { id: 'assistant', label: t('expenseHistory.assistant', 'Trợ lý'), visible: true },
  ]);

  // Mock data cho lịch sử chi tiêu
  const mockData = useMemo(() => {
    return {
      items: [
        {
          id: '123',
          time: '20/01/2023',
          points: 1000000,
          channel: SocialChannel.FACEBOOK,
          assistant: 'Đỗ Thị Huyền',
        },
        {
          id: '123',
          time: '20/01/2023',
          points: 1000000,
          channel: SocialChannel.LINKEDIN,
          assistant: 'Đỗ Thị Huyền',
        },
        {
          id: '123',
          time: '20/01/2023',
          points: 1000000,
          channel: SocialChannel.INSTAGRAM,
          assistant: 'Đỗ Thị Huyền',
        },
        {
          id: '123',
          time: '20/01/2023',
          points: 1000000,
          channel: SocialChannel.TIKTOK,
          assistant: 'Đỗ Thị Huyền',
        },
        {
          id: '123',
          time: '20/01/2023',
          points: 1000000,
          channel: SocialChannel.ZALO,
          assistant: 'Đỗ Thị Huyền',
        },
      ],
      total: 5,
    };
  }, []);

  // Xử lý khi thay đổi trang
  const handlePageChange = (page: number) => {
    setQueryParams(prev => ({ ...prev, page }));
  };

  // Xử lý khi thay đổi từ khóa tìm kiếm
  const handleSearchChange = (value: string) => {
    setSearchText(value);
    setQueryParams(prev => ({ ...prev, page: 1, search: value }));
  };

  // Định nghĩa các cột cho bảng
  const allColumns: TableColumn[] = useMemo(
    () => [
      {
        title: 'ID',
        dataIndex: 'id',
        key: 'id',
        width: '10%',
        sorter: true,
      },
      {
        title: t('expenseHistory.time', 'Thời gian'),
        dataIndex: 'time',
        key: 'time',
        width: '15%',
        sorter: true,
      },
      {
        title: t('expenseHistory.points', 'Số point'),
        dataIndex: 'points',
        key: 'points',
        width: '20%',
        sorter: true,
        render: (value: unknown) => (
          <div className="flex items-center">
            {(value as number).toLocaleString()}
            <img src={rpointImage} alt="R-Point" className="ml-2 w-4 h-4" />
          </div>
        ),
      },
      {
        title: t('expenseHistory.channel', 'Kênh'),
        dataIndex: 'channel',
        key: 'channel',
        width: '15%',
        render: (value: unknown) => {
          const channel = value as SocialChannel;
          let iconName = 'globe';
          const bgColor = 'bg-red-500';

          switch (channel) {
            case SocialChannel.FACEBOOK:
              iconName = 'facebook';
              break;
            case SocialChannel.LINKEDIN:
              iconName = 'linkedin';
              break;
            case SocialChannel.INSTAGRAM:
              iconName = 'instagram';
              break;
            case SocialChannel.TIKTOK:
              iconName = 'tiktok';
              break;
            case SocialChannel.ZALO:
              iconName = 'zalo';
              break;
            default:
              iconName = 'globe';
          }

          return (
            <div
              className={`${bgColor} w-8 h-8 rounded-full flex items-center justify-center text-white`}
            >
              <Icon name={iconName} size="sm" />
            </div>
          );
        },
      },
      {
        title: t('expenseHistory.assistant', 'Trợ lý'),
        dataIndex: 'assistant',
        key: 'assistant',
        width: '20%',
      },
    ],
    [t]
  );

  // Lọc các cột theo columnSettings
  const columns = useMemo(() => {
    const visibleColumnIds = columnSettings
      .filter(col => col.visible && col.id !== 'all')
      .map(col => col.id);
    return allColumns.filter(column => visibleColumnIds.includes(column.key));
  }, [allColumns, columnSettings]);

  // Xử lý khi click vào nút tìm kiếm
  const handleSearchClick = () => {
    setShowSearch(!showSearch);
    if (showSearch && searchText) {
      setSearchText('');
      setQueryParams(prev => ({ ...prev, page: 1, search: '' }));
    }
  };

  // Xử lý khi click vào nút lọc
  const handleFilterClick = () => {
    setShowFilterMenu(!showFilterMenu);
  };

  return (
    <div className="space-y-4">
      {/* Bộ lọc và tìm kiếm */}
      <div className="flex flex-col sm:flex-row items-start sm:items-center space-y-3 sm:space-y-0 sm:space-x-3 mb-4">
        <div className="flex items-center space-x-2">
          <Tooltip content={t('common.search', 'Tìm kiếm')} position="bottom">
            <IconCard
              icon="search"
              variant={showSearch ? 'primary' : 'default'}
              onClick={handleSearchClick}
              active={showSearch}
            />
          </Tooltip>

          <div className="relative">
            <Tooltip content={t('common.filter', 'Lọc')} position="right">
              <IconCard icon="filter" variant="default" onClick={handleFilterClick} />

              {showFilterMenu && (
                <ModernMenu
                  isOpen={showFilterMenu}
                  onClose={() => setShowFilterMenu(false)}
                  placement="bottom"
                  width="180px"
                  items={[
                    {
                      id: 'all',
                      label: t('common.all', 'Tất cả'),
                      icon: 'list',
                      onClick: () => {
                        // Xử lý khi chọn tất cả
                      },
                    },
                    {
                      id: 'shopping',
                      label: t('expenseHistory.shopping', 'Mua sắm'),
                      icon: 'shopping-cart',
                      onClick: () => {
                        // Xử lý khi chọn mua sắm
                      },
                    },
                    {
                      id: 'food',
                      label: t('expenseHistory.food', 'Ăn uống'),
                      icon: 'utensils',
                      onClick: () => {
                        // Xử lý khi chọn ăn uống
                      },
                    },
                  ]}
                />
              )}
            </Tooltip>
          </div>

          <div className="relative">
            <Tooltip content={t('common:columns', 'Cột hiển thị')} position="right">
              <IconCard
                icon="layers"
                variant="default"
                onClick={() => setShowColumnMenu(!showColumnMenu)}
              />

              {showColumnMenu && (
                <ModernMenu
                  isOpen={showColumnMenu}
                  onClose={() => setShowColumnMenu(false)}
                  placement="bottom"
                  width="220px"
                  items={[
                    {
                      id: 'header',
                      label: (
                        <div className="font-medium text-sm mb-1">
                          {t('common:columns', 'Cột hiển thị')}
                        </div>
                      ),
                      divider: true,
                    },
                    {
                      id: 'all',
                      label: (
                        <div className="flex items-center">
                          <Checkbox
                            id="column-all"
                            {...(columnSettings.find(col => col.id === 'all')?.visible !== undefined && {
                              checked: columnSettings.find(col => col.id === 'all')?.visible
                            })}
                            onChange={checked => {
                              const updatedColumns = columnSettings.map(col => ({
                                ...col,
                                visible: checked,
                              }));
                              setColumnSettings(updatedColumns);
                            }}
                            variant="filled"
                            size="sm"
                            label={
                              <span className="ml-1 text-sm">
                                {t('common:selectAll', 'Chọn tất cả')}
                              </span>
                            }
                          />
                        </div>
                      ),
                    },
                    ...columnSettings
                      .filter(column => column.id !== 'all')
                      .map(column => ({
                        id: column.id,
                        label: (
                          <div className="flex items-center">
                            <Checkbox
                              id={`column-${column.id}`}
                              checked={column.visible}
                              onChange={checked => {
                                const updatedColumns = [...columnSettings];
                                const columnToUpdate = updatedColumns.find(
                                  col => col.id === column.id
                                );
                                if (columnToUpdate) {
                                  columnToUpdate.visible = checked;
                                }

                                // Cập nhật trạng thái "Chọn tất cả"
                                const allColumn = updatedColumns.find(col => col.id === 'all');
                                if (allColumn) {
                                  const otherColumns = updatedColumns.filter(
                                    col => col.id !== 'all'
                                  );
                                  allColumn.visible = otherColumns.every(col => col.visible);
                                }

                                setColumnSettings(updatedColumns);
                              }}
                              variant="filled"
                              size="sm"
                              label={<span className="ml-1 text-sm">{column.label}</span>}
                            />
                          </div>
                        ),
                      })),
                  ]}
                />
              )}
            </Tooltip>
          </div>
        </div>

        {/* Chỉ hiển thị SearchBar khi showSearch = true */}
        <div className="w-full sm:w-auto">
          <SearchBar
            visible={showSearch}
            value={searchText}
            onChange={handleSearchChange}
            onToggle={handleSearchClick}
            maxWidth="100%"
            variant="flat"
            autoFocus={true}
            showSearchIcon={false}
            className="w-full"
            placeholder={t('expenseHistory.searchPlaceholder', 'Tìm kiếm theo ID hoặc ngày')}
          />
        </div>
      </div>

      <Table
        data={mockData.items}
        columns={columns}
        loading={false}
        sortable={true}
        pagination={{
          current: queryParams.page,
          pageSize: queryParams.pageSize,
          total: mockData.total,
          onChange: handlePageChange,
          pageSizeOptions: [5, 10, 20, 50],
        }}
        bordered
      />
    </div>
  );
};

export default ExpenseHistoryTable;
