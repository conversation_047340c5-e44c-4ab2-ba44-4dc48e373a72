import { useState, useCallback, useMemo } from 'react';
import { SortDirection, TypeAgentSortBy, GetTypeAgentsQueryDto } from '../types';
import { TypeAgentFilterData } from '../components/filters';

export interface UseTypeAgentFilterOptions {
  /**
   * Giá trị mặc định cho filter
   */
  defaultFilters?: Partial<TypeAgentFilterData>;

  /**
   * Callback khi filter thay đổi
   */
  onFilterChange?: (filters: TypeAgentFilterData) => void;
}

export interface UseTypeAgentFilterReturn {
  /**
   * Giá trị filter hiện tại
   */
  filters: TypeAgentFilterData;

  /**
   * Query parameters cho API
   */
  queryParams: GetTypeAgentsQueryDto;

  /**
   * Cập nhật filter
   */
  updateFilter: (field: keyof TypeAgentFilterData, value: string | boolean | TypeAgentSortBy | SortDirection | undefined) => void;

  /**
   * Cập nhật nhiều filter cùng lúc
   */
  updateFilters: (newFilters: Partial<TypeAgentFilterData>) => void;

  /**
   * Reset filter về mặc định
   */
  resetFilters: () => void;

  /**
   * Kiểm tra có filter nào được áp dụng không
   */
  hasActiveFilters: boolean;

  /**
   * Số lượng filter đang được áp dụng
   */
  activeFilterCount: number;

  /**
   * Preset filters
   */
  presets: {
    systemAgents: () => void;
    userAgents: () => void;
    sortByName: () => void;
    sortByDate: () => void;
    ascending: () => void;
    descending: () => void;
  };
}

/**
 * Hook để quản lý filter state cho Type Agent
 */
export const useTypeAgentFilter = (
  options: UseTypeAgentFilterOptions = {}
): UseTypeAgentFilterReturn => {
  const { defaultFilters = {}, onFilterChange } = options;

  // Default filter values - wrapped in useMemo to prevent re-creation
  const defaultFilterValues: TypeAgentFilterData = useMemo(() => ({
    search: '',
    isSystem: true, // Mặc định lấy system agents
    sortBy: TypeAgentSortBy.CREATED_AT,
    sortDirection: SortDirection.DESC,
    ...defaultFilters
  }), [defaultFilters]);

  // Filter state
  const [filters, setFilters] = useState<TypeAgentFilterData>(defaultFilterValues);

  // Update single filter
  const updateFilter = useCallback((field: keyof TypeAgentFilterData, value: string | boolean | TypeAgentSortBy | SortDirection | undefined) => {
    setFilters(prev => {
      const newFilters = { ...prev, [field]: value };
      onFilterChange?.(newFilters);
      return newFilters;
    });
  }, [onFilterChange]);

  // Update multiple filters
  const updateFilters = useCallback((newFilters: Partial<TypeAgentFilterData>) => {
    setFilters(prev => {
      const updatedFilters = { ...prev, ...newFilters };
      onFilterChange?.(updatedFilters);
      return updatedFilters;
    });
  }, [onFilterChange]);

  // Reset filters
  const resetFilters = useCallback(() => {
    setFilters(defaultFilterValues);
    onFilterChange?.(defaultFilterValues);
  }, [defaultFilterValues, onFilterChange]);

  // Convert to API query params
  const queryParams = useMemo((): GetTypeAgentsQueryDto => {
    const params: GetTypeAgentsQueryDto = {};

    if (filters.search) {
      params.search = filters.search;
    }
    if (filters.isSystem !== undefined) {
      params.isSystem = filters.isSystem;
    }
    if (filters.sortBy !== undefined) {
      params.sortBy = filters.sortBy;
    }
    if (filters.sortDirection !== undefined) {
      params.sortDirection = filters.sortDirection;
    }

    return params;
  }, [filters]);

  // Check if any filters are active
  const hasActiveFilters = useMemo(() => {
    return !!(
      filters.search ||
      filters.isSystem !== defaultFilterValues.isSystem ||
      filters.sortBy !== defaultFilterValues.sortBy ||
      filters.sortDirection !== defaultFilterValues.sortDirection
    );
  }, [filters, defaultFilterValues]);

  // Count active filters
  const activeFilterCount = useMemo(() => {
    return [
      !!filters.search,
      filters.isSystem !== defaultFilterValues.isSystem,
      filters.sortBy !== defaultFilterValues.sortBy,
      filters.sortDirection !== defaultFilterValues.sortDirection
    ].filter(Boolean).length;
  }, [filters, defaultFilterValues]);

  // Preset filters
  const presets = useMemo(() => ({
    systemAgents: () => updateFilter('isSystem', true),
    userAgents: () => updateFilter('isSystem', false),
    sortByName: () => updateFilter('sortBy', TypeAgentSortBy.NAME),
    sortByDate: () => updateFilter('sortBy', TypeAgentSortBy.CREATED_AT),
    ascending: () => updateFilter('sortDirection', SortDirection.ASC),
    descending: () => updateFilter('sortDirection', SortDirection.DESC),
  }), [updateFilter]);

  return {
    filters,
    queryParams,
    updateFilter,
    updateFilters,
    resetFilters,
    hasActiveFilters,
    activeFilterCount,
    presets
  };
};

/**
 * Hook để quản lý filter với pagination
 */
export const useTypeAgentFilterWithPagination = (
  options: UseTypeAgentFilterOptions & {
    defaultPage?: number;
    defaultLimit?: number;
  } = {}
) => {
  const { defaultPage = 1, defaultLimit = 10, ...filterOptions } = options;

  const [page, setPage] = useState(defaultPage);
  const [limit, setLimit] = useState(defaultLimit);

  const filterHook = useTypeAgentFilter({
    ...filterOptions,
    onFilterChange: (filters) => {
      setPage(1); // Reset page when filter changes
      filterOptions.onFilterChange?.(filters);
    }
  });

  const queryParamsWithPagination = useMemo((): GetTypeAgentsQueryDto => {
    return {
      ...filterHook.queryParams,
      page,
      limit,
    };
  }, [filterHook.queryParams, page, limit]);

  return {
    ...filterHook,
    page,
    limit,
    setPage,
    setLimit,
    queryParams: queryParamsWithPagination,
  };
};
