import React from 'react';
import { ImportedConversation } from '../user-data-fine-tune/types/user-data-fine-tune.types';
import ChatLayoutValidation from './ChatLayoutValidation';

interface ValidationDataFormProps {
  /**
   * Callback khi conversations thay đổi
   */
  onConversationsChange?: (conversations: ImportedConversation[]) => void;
}

/**
 * Component hiển thị ChatLayoutValidation cho validation data
 * Role Logic: SystemRole → UserRole → UserRole/AssistantRole
 */
const ValidationDataForm: React.FC<ValidationDataFormProps> = ({ onConversationsChange }) => {
  // Handle conversations change từ ChatLayoutValidation
  const handleConversationsChange = (updatedConversations: ImportedConversation[]) => {
  
    // Notify parent component
    if (onConversationsChange) {
     
      onConversationsChange(updatedConversations);
    } else {
      console.warn('📋 [ValidationDataForm] No onConversationsChange callback provided!');
    }
  };

  return (
    <div className="h-full">
      {/* Validation Data Chat Layout Container - Full height */}
      <div className="h-full">
        <ChatLayoutValidation onConversationsChange={handleConversationsChange} />
      </div>
    </div>
  );
};

export default ValidationDataForm;
