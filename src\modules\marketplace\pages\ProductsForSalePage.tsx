import React, { useMemo, useState, useEffect, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate, useParams } from 'react-router-dom';
import {
  Alert,
  Button,
  Card,
  Chip,
  Icon,
  IconCard,
  Loading,
  Modal,
  ModernMenu,
  ResponsiveImage,
  Table,
  Typography,
} from '@/shared/components/common';
import { TableColumn } from '@/shared/components/common/Table/types';
import MenuIconBar from '@/modules/components/menu-bar/MenuIconBar';

import SlideInForm from '@/shared/components/common/SlideInForm';
import useSlideForm from '@/shared/hooks/useSlideForm';
import AddProductForm from '../components/forms/AddProductForm';
import ProductForSaleForm, { ProductForSaleFormValues } from '../components/forms/ProductForSaleForm';
import { useQuery } from '@tanstack/react-query';
import {
  MarketplaceApiService,
  ApiProduct,
  CreateProductDto,
  UpdateProductDto,
  UserProductQueryParams,
} from '../services/marketplace-api.service';
import { PRODUCT_QUERY_KEYS } from '../constants/product-query-keys';
import { formatPrice } from '../utils/price-formatter';
import { NotificationUtil } from '@/shared/utils/notification';
import { useDataTable, useDataTableConfig } from '@/shared/hooks/table';
import { useActiveFilters } from '@/shared/hooks/filters';

import {
  useCreateProduct,
  useUpdateProduct,
  useSubmitProductForApproval,
  useCancelProductSubmission,
  useBatchDeleteProducts,
} from '../hooks/useUserProducts';
import { CreateUserProductDto, UserProductCategory } from '../types/user-product.types';
// import { PurchaseHistoryResponseSchema } from '@/modules/rpoint/schemas/rpoint.schema';

/**
 * Trang quản lý sản phẩm đăng bán
 */
const ProductsForSalePage: React.FC = () => {
  const { t } = useTranslation('marketplace');
  const navigate = useNavigate();
  const { productId } = useParams<{ productId: string }>();

  // State cho selected product
  const [selectedProduct, setSelectedProduct] = useState<ApiProduct | null>(null);

  // State cho row selection và bulk actions
  const [selectedRowKeys, setSelectedRowKeys] = useState<number[]>([]);
  const [showBulkDeleteConfirm, setShowBulkDeleteConfirm] = useState(false);

  // State quản lý menu hành động
  const [actionMenu, setActionMenu] = useState<{
    visible: boolean;
    recordId: number | null;
    recordName: string | null;
  }>({
    visible: false,
    recordId: null,
    recordName: null,
  });



  // Filter options cho status
  const filterOptions = useMemo(() => [
    { id: 'all', label: t('common:all', 'Tất cả'), value: 'all' },
    { id: 'DRAFT', label: t('marketplace:productsForSale.status.draft', 'Bản nháp'), value: 'DRAFT' },
    { id: 'PENDING', label: t('marketplace:productsForSale.status.pending', 'Chờ duyệt'), value: 'PENDING' },
    { id: 'APPROVED', label: t('marketplace:productsForSale.status.approved', 'Đã duyệt'), value: 'APPROVED' },
    { id: 'REJECTED', label: t('marketplace:productsForSale.status.rejected', 'Từ chối'), value: 'REJECTED' },
  ], [t]);

  // Hàm tạo query parameters
  const createQueryParams = useCallback(
    (params: {
      page: number;
      pageSize: number;
      searchTerm: string;
      sortBy: string | null;
      sortDirection: 'ASC' | 'DESC' | null;
      filterValue: string | number | boolean | undefined;
    }): UserProductQueryParams => {
      const queryParams: UserProductQueryParams = {
        page: params.page,
        limit: params.pageSize,
      };

      if (params.searchTerm) {
        queryParams.search = params.searchTerm;
      }

      if (params.filterValue && typeof params.filterValue === 'string' && params.filterValue !== 'all') {
        queryParams.status = params.filterValue as 'DRAFT' | 'PENDING' | 'APPROVED' | 'REJECTED' | 'DELETED';
      }

      if (params.sortBy && params.sortDirection) {
        queryParams.sortBy = params.sortBy;
        queryParams.sortDirection = params.sortDirection;
      }

      return queryParams;
    },
    []
  );

  // Sử dụng hook animation cho form Add
  const { isVisible: isAddFormVisible, showForm: showAddForm, hideForm: hideAddForm } = useSlideForm();

  // Sử dụng hook animation cho form
  const { isVisible, showForm, hideForm } = useSlideForm();

  // Columns cho bảng
  const columns: TableColumn<ApiProduct>[] = [
    { key: 'id', title: 'ID', dataIndex: 'id', width: '5%' },
    {
      key: 'images',
      title: t('marketplace:productsForSale.table.image', 'Ảnh sản phẩm'),
      dataIndex: 'images',
      width: '10%',
      render: (_: unknown, record: ApiProduct) => (
        <div className="w-12 h-12 rounded overflow-hidden bg-gray-100">
          {record.images && record.images.length > 0 ? (
            <ResponsiveImage
              src={typeof record.images[0] === 'string' ? record.images[0] : record.images[0]?.url || '/placeholder-image.jpg'}
              alt="Product"
              className="w-full h-full object-cover"
            />
          ) : (
            <div className="w-full h-full flex items-center justify-center">
              <Icon name="image" size="sm" className="text-gray-400" />
            </div>
          )}
        </div>
      ),
    },
    { key: 'name', title: t('marketplace:productsForSale.table.name', 'Tên sản phẩm'), dataIndex: 'name', width: '20%' },
    {
      key: 'soldCount',
      title: t('marketplace:productsForSale.table.soldCount', 'Đã bán'),
      dataIndex: 'soldCount',
      width: '8%',
      render: (value: unknown) => <span className="text-green-600 font-medium">{String(value || 0)}</span>,
    },
    {
      key: 'createdAt',
      title: t('marketplace:productsForSale.table.createdAt', 'Ngày tạo'),
      dataIndex: 'createdAt',
      width: '12%',
      render: (value: unknown) => new Date(value as string).toLocaleDateString('vi-VN'),
    },
    {
      key: 'category',
      title: t('marketplace:productsForSale.table.category', 'Thể loại'),
      dataIndex: 'category',
      width: '10%',
      render: (value: unknown): React.ReactNode => {
        const categoryMap: Record<string, string> = {
          AGENT: t('marketplace:productsForSale.categories.agent', 'AI Agent'),
          KNOWLEDGE_FILE: t('marketplace:productsForSale.categories.knowledgeFile', 'Tài liệu'),
          FUNCTION: t('marketplace:productsForSale.categories.function', 'Chức năng'),
          FINETUNE: t('marketplace:productsForSale.categories.finetune', 'Fine-tune'),
          STRATEGY: t('marketplace:productsForSale.categories.strategy', 'Chiến lược'),
        };
        return <span>{categoryMap[value as string] || String(value || '')}</span>;
      },
    },
    {
      key: 'status',
      title: t('marketplace:productsForSale.table.status', 'Trạng thái'),
      dataIndex: 'status',
      width: '10%',
      render: (value: unknown) => getStatusChip(value as string),
    },
    {
      key: 'discountedPrice',
      title: t('marketplace:productsForSale.table.price', 'Giá bán'),
      dataIndex: 'discountedPrice',
      width: '15%',
      render: (value: unknown, record: ApiProduct) => (
        <div className="flex items-center">
          <div className="flex flex-col">
            <div className="flex items-center">
              <span className="mr-1">{formatPrice(value as number)}</span>
              <Icon name="rpoint" size="sm" className="text-red-600" />
            </div>
            {record.listedPrice > record.discountedPrice && (
              <div className="flex items-center text-gray-500 text-xs">
              </div>
            )}
          </div>
        </div>
      ),
    },
    {
      key: 'actions',
      title: t('marketplace:productsForSale.table.actions', 'Thao tác'),
      width: '10%',
      render: (_: unknown, record: ApiProduct) => (
        <div className="relative">
          <IconCard
            icon="menu"
            variant="default"
            size="sm"
            onClick={() => {
              setActionMenu({
                visible: true,
                recordId: record.id,
                recordName: record.name,
              });
            }}
          />
          {actionMenu.visible && actionMenu.recordId === record.id && (
            <ModernMenu
              isOpen={true}
              onClose={() => setActionMenu({ visible: false, recordId: null, recordName: null })}
              placement="left"
              items={[
                {
                  id: 'edit',
                  label: t('common:edit', 'Chỉnh sửa'),
                  icon: 'edit',
                  onClick: () => {
                    handleEdit(record);
                    setActionMenu({ visible: false, recordId: null, recordName: null });
                  },
                },
                // Submit for approval button - chỉ hiển thị khi status là DRAFT
                ...(record.status === 'DRAFT' ? [{
                  id: 'submit',
                  label: t('marketplace:productsForSale.submitForApproval', 'Gửi duyệt'),
                  icon: 'send',
                  onClick: () => {
                    handleSubmitForApproval(record);
                    setActionMenu({ visible: false, recordId: null, recordName: null });
                  },
                  disabled: submitForApprovalMutation.isPending,
                }] : []),
                // Cancel submission button - chỉ hiển thị khi status là PENDING
                ...(record.status === 'PENDING' ? [{
                  id: 'cancel',
                  label: t('marketplace:productsForSale.cancelSubmission', 'Hủy gửi duyệt'),
                  icon: 'x',
                  onClick: () => {
                    handleCancelSubmission(record);
                    setActionMenu({ visible: false, recordId: null, recordName: null });
                  },
                  disabled: cancelSubmissionMutation.isPending,
                }] : []),

              ]}
            />
          )}
        </div>
      ),
    },
  ];

  // Sử dụng hook useDataTable với cấu hình mặc định
  const dataTable = useDataTable(
    useDataTableConfig<ApiProduct, UserProductQueryParams>({
      columns,
      filterOptions,
      showDateFilter: false,
      createQueryParams,
    })
  );

  // API Hooks
  const createProductMutation = useCreateProduct();
  const updateProductMutation = useUpdateProduct();
  const submitForApprovalMutation = useSubmitProductForApproval();
  const cancelSubmissionMutation = useCancelProductSubmission();
  const batchDeleteMutation = useBatchDeleteProducts();

  // Lấy danh sách sản phẩm của user từ API
  const {
    data: userProductsData,
    isLoading: isLoadingProducts,
    error,
    refetch,
  } = useQuery({
    queryKey: [...PRODUCT_QUERY_KEYS.USER_PRODUCTS, dataTable.queryParams],
    queryFn: () => MarketplaceApiService.getUserProducts(dataTable.queryParams),
    staleTime: 2 * 60 * 1000, // 2 minutes
  });

  // Lấy chi tiết sản phẩm nếu có productId trong URL
  const {
    data: productDetail,
    isLoading: isLoadingDetail,
  } = useQuery({
    queryKey: PRODUCT_QUERY_KEYS.USER_PRODUCT_DETAIL(Number(productId || 0)),
    queryFn: () => MarketplaceApiService.getUserProductDetail(Number(productId)),
    enabled: !!productId,
    staleTime: 2 * 60 * 1000,
  });

  // Effect để auto-open form khi có productId
  useEffect(() => {
    if (productId && productDetail && !selectedProduct) {
      console.log('🔍 Auto-opening edit form for product:', productId, productDetail);
      setSelectedProduct(productDetail);
      showForm();
    }
  }, [productId, productDetail, selectedProduct, showForm]);

  // Status mapping
  const getStatusChip = (status: string) => {
    switch (status) {
      case 'APPROVED':
        return <Chip variant="success" size="sm">{t('marketplace:productsForSale.status.approved', 'Đã duyệt')}</Chip>;
      case 'PENDING':
        return <Chip variant="warning" size="sm">{t('marketplace:productsForSale.status.pending', 'Chờ duyệt')}</Chip>;
      case 'REJECTED':
        return <Chip variant="danger" size="sm">{t('marketplace:productsForSale.status.rejected', 'Từ chối')}</Chip>;
      case 'DRAFT':
        return <Chip variant="default" size="sm">{t('marketplace:productsForSale.status.draft', 'Bản nháp')}</Chip>;
      case 'DELETED':
        return <Chip variant="danger" size="sm">{t('marketplace:productsForSale.status.deleted', 'Đã xóa')}</Chip>;
      default:
        return <Chip variant="default" size="sm">{status}</Chip>;
    }
  };



  // Cập nhật dataTable với data từ API
  useEffect(() => {
    if (userProductsData) {
      dataTable.updateTableData(userProductsData, isLoadingProducts);
    }
  }, [userProductsData, isLoadingProducts, dataTable]);

  // Sử dụng hook useActiveFilters để quản lý các hàm xử lý bộ lọc
  const { handleClearSearch, handleClearFilter, handleClearSort, handleClearAll, getFilterLabel } =
    useActiveFilters({
      t,
      handleSearch: dataTable.tableData.handleSearch,
      setSelectedFilterId: dataTable.filter.setSelectedId,
      setDateRange: dataTable.dateRange.setDateRange,
      handleSortChange: dataTable.tableData.handleSortChange,
      selectedFilterValue: dataTable.filter.selectedValue,
      filterValueLabelMap: {
        DRAFT: t('marketplace:productsForSale.status.draft', 'Bản nháp'),
        PENDING: t('marketplace:productsForSale.status.pending', 'Chờ duyệt'),
        APPROVED: t('marketplace:productsForSale.status.approved', 'Đã duyệt'),
        REJECTED: t('marketplace:productsForSale.status.rejected', 'Từ chối'),
      },
    });

  const handleAdd = () => {
    setSelectedProduct(null); // Reset selected product when adding new
    showAddForm(); // Mở Add form
  };

  // Xử lý chỉnh sửa
  const handleEdit = (product: ApiProduct) => {
    setSelectedProduct(product);
    showForm();
  };

  // Xử lý submit form Add Product
  const handleAddProductSubmit = async (values: CreateUserProductDto): Promise<{
    code: number;
    message: string;
    result: {
      id: string;
      name: string;
      description: string;
      listedPrice: string;
      discountedPrice: string;
      category: string;
      sourceId: string;
      createdAt: string;
      updatedAt: string;
      seller: {
        name: string;
        avatar: string;
        type: string;
      };
      status: string;
      uploadUrls: {
        productId: string;
        imagesUploadUrls?: Array<{
          url: string;
          key: string;
          index: number;
        }>;
        detailUploadUrl?: {
          url: string;
          key: string;
          expiresAt: number;
        };
        userManualUploadUrl?: {
          url: string;
          key: string;
          expiresAt: number;
        };
      };
    };
  }> => {
    try {
      console.log('🔍 [ADD PRODUCT] Starting product creation...');
      console.log('🔍 [ADD PRODUCT] Form values:', values);

      // Chuyển đổi từ CreateUserProductDto sang CreateProductDto
      // Map UserProductCategory sang category của API
      let apiCategory: 'AGENT' | 'KNOWLEDGE_FILE' | 'FUNCTION' | 'FINETUNE' | 'STRATEGY';
      switch (values.category) {
        case UserProductCategory.AGENT:
          apiCategory = 'AGENT';
          break;
        case UserProductCategory.KNOWLEDGE_FILE:
          apiCategory = 'KNOWLEDGE_FILE';
          break;
        case UserProductCategory.OTHER:
        default:
          apiCategory = 'FUNCTION'; // Map OTHER thành FUNCTION
          break;
      }

      const createData: CreateProductDto = {
        name: values.name,
        description: values.description,
        listedPrice: values.listedPrice,
        discountedPrice: values.discountedPrice,
        category: apiCategory,
        sourceId: values.sourceId || 'temp-source-id', // Sử dụng sourceId từ form hoặc giá trị mặc định
        imagesMediaTypes: values.imagesMediaTypes || [],
        ...(values.userManualMediaType && { userManualMediaType: values.userManualMediaType }),
        ...(values.detailMediaType && { detailMediaType: values.detailMediaType }),
      };

      console.log('🔍 [ADD PRODUCT] API request data:', createData);

      // Gọi API tạo sản phẩm - response từ marketplace-api.service
      const apiResponse = await createProductMutation.mutateAsync(createData);
      console.log('✅ [ADD PRODUCT] API response:', apiResponse);
      console.log('✅ [ADD PRODUCT] API response type:', typeof apiResponse);
      console.log('✅ [ADD PRODUCT] API response structure:', JSON.stringify(apiResponse, null, 2));

      // Trả về response với đúng cấu trúc uploadUrls từ API thực tế
      // API trả về response với cấu trúc khác so với marketplace-api.service interface
      // Cần trả về đúng format mà AddProductForm mong đợi
      const actualApiResponse = apiResponse as unknown as {
        code: number;
        message: string;
        result: {
          id: string;
          name: string;
          description: string;
          listedPrice: string;
          discountedPrice: string;
          category: string;
          sourceId: string;
          createdAt: string;
          updatedAt: string;
          seller: {
            name: string;
            avatar: string;
            type: string;
          };
          status: string;
          uploadUrls: {
            productId: string;
            imagesUploadUrls?: Array<{
              url: string;
              key: string;
              index: number;
            }>;
            detailUploadUrl?: {
              url: string;
              key: string;
              expiresAt: number;
            };
            userManualUploadUrl?: {
              url: string;
              key: string;
              expiresAt: number;
            };
          };
        };
      };

      console.log('✅ [ADD PRODUCT] Returning actual API response structure');
      return actualApiResponse;
    } catch (error) {
      console.error('❌ [ADD PRODUCT] Error creating product:', error);
      throw error; // Re-throw để AddProductForm có thể xử lý error
    }
  };

  // Xử lý success sau khi upload hoàn thành
  const handleAddProductSuccess = () => {
    console.log('✅ [ADD PRODUCT] Upload completed, closing form...');
    setSelectedProduct(null);
    hideAddForm();
    // Refetch data để cập nhật danh sách
    refetch();
  };

  // Xử lý submit form
  const handleSubmit = async (values: ProductForSaleFormValues & {
    imageFiles?: File[];
    imageOperations?: Array<{
      operation: 'ADD' | 'DELETE';
      key?: string;
      index?: number;
      mimeType?: string;
    }>;
    hasImageChanged?: boolean;
    hasDetailChanged?: boolean;
    hasUserManualChanged?: boolean;
  }, submitForApproval = false) => {
    try {
      if (selectedProduct) {
        // Sử dụng imageOperations từ form hoặc tạo mới nếu không có
        const imageOperations = values.imageOperations || [];

        console.log('🔍 [IMAGE OPERATIONS] Received operations from form:', imageOperations);
        console.log('🔍 [IMAGE OPERATIONS] imageFiles:', values.imageFiles?.map(f => f.name));

        // Debug flags
        console.log('🔍 [FLAGS] hasDetailChanged:', values.hasDetailChanged);
        console.log('🔍 [FLAGS] hasUserManualChanged:', values.hasUserManualChanged);
        console.log('🔍 [FLAGS] detail content:', values.detail?.substring(0, 50) + '...');
        console.log('🔍 [FLAGS] userManual content:', values.userManual?.substring(0, 50) + '...');

        // CẬP NHẬT sản phẩm hiện tại - KHÔNG tạo bản mới
        const updateData: UpdateProductDto = {
          productInfo: {
            name: values.name,
            description: values.description || '',
            // Backend yêu cầu listedPrice và discountedPrice bắt buộc
            listedPrice: values.price && values.price > 0 ? values.price : selectedProduct.listedPrice,
            discountedPrice: values.price && values.price > 0 ? values.price : selectedProduct.discountedPrice,
            // Note: User DTO không có category field
          },
          images: imageOperations, // Optional theo user DTO
          detailEdited: values.hasDetailChanged || false, // Sử dụng flag thay vì check content
          userManual: values.hasUserManualChanged || false, // Sử dụng flag thay vì check content
          updateOption: submitForApproval ? 'SUBMIT_FOR_APPROVAL' : 'SAVE_DRAFT',
        };

        console.log('🔍 UPDATING product ID:', selectedProduct.id);
        console.log('🔍 Selected product images:', selectedProduct.images);
        console.log('🔍 Has image changed:', values.hasImageChanged);
        console.log('🔍 Image operations:', imageOperations);
        console.log('🔍 Update data:', JSON.stringify(updateData, null, 2));
        console.log('🔍 Image files:', values.imageFiles);

        // Gọi API cập nhật - KHÔNG tạo mới
        const updateResponse = await updateProductMutation.mutateAsync({
          productId: selectedProduct.id,
          data: updateData,
        });

        console.log('✅ Product updated successfully:', updateResponse);

        // Tạo mảng promises cho tất cả uploads
        const allUploadPromises: Promise<void>[] = [];

        // Upload detail content nếu có thay đổi
        if (values.hasDetailChanged && values.detail && updateResponse.presignedUrlDetail) {
          console.log('📤 [DETAIL] Uploading detail content to presigned URL...');
          const detailUploadPromise = fetch(updateResponse.presignedUrlDetail, {
            method: 'PUT',
            headers: {
              'Content-Type': 'text/plain',
            },
            body: values.detail,
          }).then(uploadResponse => {
            if (uploadResponse.ok) {
              console.log('✅ [DETAIL] Detail content uploaded successfully');
            } else {
              console.error('❌ [DETAIL] Failed to upload detail content:', uploadResponse.status);
              throw new Error(`Failed to upload detail content: ${uploadResponse.status}`);
            }
          });
          allUploadPromises.push(detailUploadPromise);
        }

        // Upload userManual content nếu có thay đổi
        if (values.hasUserManualChanged && values.userManual && updateResponse.presignedUrlUserManual) {
          console.log('📤 [USER_MANUAL] Uploading user manual content to presigned URL...');
          const userManualUploadPromise = fetch(updateResponse.presignedUrlUserManual, {
            method: 'PUT',
            headers: {
              'Content-Type': 'text/plain',
            },
            body: values.userManual,
          }).then(uploadResponse => {
            if (uploadResponse.ok) {
              console.log('✅ [USER_MANUAL] User manual content uploaded successfully');
            } else {
              console.error('❌ [USER_MANUAL] Failed to upload user manual content:', uploadResponse.status);
              throw new Error(`Failed to upload user manual content: ${uploadResponse.status}`);
            }
          });
          allUploadPromises.push(userManualUploadPromise);
        }

        // Upload images nếu có ảnh mới
        if (values.imageFiles && values.imageFiles.length > 0 && updateResponse.presignedUrlImage && updateResponse.presignedUrlImage.length > 0) {
          console.log('📤 [IMAGES] Uploading images to S3...');
          const imageUploadPromises = values.imageFiles.map(async (file, index) => {
            const presignedUrl = updateResponse.presignedUrlImage?.[index];
            if (!presignedUrl) return;

            const response = await fetch(presignedUrl.uploadUrl, {
              method: 'PUT',
              body: file,
              headers: {
                'Content-Type': file.type,
              },
            });

            if (response.ok) {
              console.log(`✅ [IMAGES] Uploaded image ${index + 1} successfully`);
            } else {
              console.error(`❌ [IMAGES] Failed to upload image ${index + 1}:`, response.status);
              throw new Error(`Failed to upload image ${index + 1}: ${response.status}`);
            }
          });
          allUploadPromises.push(...imageUploadPromises);
        }

        // Đợi tất cả uploads hoàn thành
        if (allUploadPromises.length > 0) {
          try {
            await Promise.all(allUploadPromises);
            console.log('🎉 All uploads completed successfully');
          } catch (error) {
            console.error('❌ Error during uploads:', error);
            // Không throw error để không block việc cập nhật thông tin khác
          }
        }
      } else {
        // Tạo sản phẩm mới - Logic tương tự như edit
        console.log('🔍 [CREATE] Starting product creation...');
        console.log('🔍 [CREATE] Form values:', values);
        console.log('🔍 [CREATE] Image files:', values.imageFiles);
        console.log('🔍 [CREATE] Detail content length:', values.detail?.length || 0);
        console.log('🔍 [CREATE] UserManual content length:', values.userManual?.length || 0);

        // Tạo data cho API create
        const createData: CreateProductDto = {
          name: values.name,
          description: values.description || '',
          listedPrice: values.price || 0,
          discountedPrice: values.price || 0,
          category: (values.category as unknown) as 'AGENT' | 'KNOWLEDGE_FILE' | 'FUNCTION' | 'FINETUNE' | 'STRATEGY',
          sourceId: 'temp-source-id', // TODO: Cần xử lý sourceId thực tế
          // Chỉ gửi media types nếu có content tương ứng
          imagesMediaTypes: values.imageFiles?.map(file => file.type) || [],
          ...(values.userManual && values.userManual.trim() && { userManualMediaType: 'text/html' }),
          ...(values.detail && values.detail.trim() && { detailMediaType: 'text/html' }),
        };

        console.log('🔍 [CREATE] API request data:', createData);

        // Gọi API tạo sản phẩm
        const createResponse = await createProductMutation.mutateAsync(createData);
        console.log('✅ [CREATE] Product created successfully:', createResponse);

        // Tạo mảng promises cho tất cả uploads (tương tự logic edit)
        const allUploadPromises: Promise<void>[] = [];

        // Upload detail content nếu có
        if (values.detail && values.detail.trim() && createResponse.presignedUrlDetail) {
          console.log('📤 [CREATE-DETAIL] Uploading detail content to presigned URL...');
          const detailUploadPromise = fetch(createResponse.presignedUrlDetail, {
            method: 'PUT',
            headers: {
              'Content-Type': 'text/html',
            },
            body: values.detail,
          }).then(uploadResponse => {
            if (uploadResponse.ok) {
              console.log('✅ [CREATE-DETAIL] Detail content uploaded successfully');
            } else {
              console.error('❌ [CREATE-DETAIL] Failed to upload detail content:', uploadResponse.status);
              throw new Error(`Failed to upload detail content: ${uploadResponse.status}`);
            }
          });
          allUploadPromises.push(detailUploadPromise);
        }

        // Upload userManual content nếu có
        if (values.userManual && values.userManual.trim() && createResponse.presignedUrlUserManual) {
          console.log('📤 [CREATE-USER_MANUAL] Uploading user manual content to presigned URL...');
          const userManualUploadPromise = fetch(createResponse.presignedUrlUserManual, {
            method: 'PUT',
            headers: {
              'Content-Type': 'text/html',
            },
            body: values.userManual,
          }).then(uploadResponse => {
            if (uploadResponse.ok) {
              console.log('✅ [CREATE-USER_MANUAL] User manual content uploaded successfully');
            } else {
              console.error('❌ [CREATE-USER_MANUAL] Failed to upload user manual content:', uploadResponse.status);
              throw new Error(`Failed to upload user manual content: ${uploadResponse.status}`);
            }
          });
          allUploadPromises.push(userManualUploadPromise);
        }

        // Upload images nếu có ảnh mới
        if (values.imageFiles && values.imageFiles.length > 0 && createResponse.presignedUrlImage && createResponse.presignedUrlImage.length > 0) {
          console.log('📤 [CREATE-IMAGES] Uploading images to S3...');
          const imageUploadPromises = values.imageFiles.map(async (file, index) => {
            const presignedUrl = createResponse.presignedUrlImage?.[index];
            if (!presignedUrl) {
              console.warn(`⚠️ [CREATE-IMAGES] No presigned URL for image ${index + 1}`);
              return;
            }

            console.log(`📤 [CREATE-IMAGES] Uploading image ${index + 1}: ${file.name}`);
            const response = await fetch(presignedUrl.uploadUrl, {
              method: 'PUT',
              body: file,
              headers: {
                'Content-Type': file.type,
              },
            });

            if (response.ok) {
              console.log(`✅ [CREATE-IMAGES] Uploaded image ${index + 1} successfully`);
            } else {
              console.error(`❌ [CREATE-IMAGES] Failed to upload image ${index + 1}:`, response.status);
              throw new Error(`Failed to upload image ${index + 1}: ${response.status}`);
            }
          });
          allUploadPromises.push(...imageUploadPromises);
        }

        // Đợi tất cả uploads hoàn thành
        if (allUploadPromises.length > 0) {
          try {
            console.log(`🔄 [CREATE] Starting ${allUploadPromises.length} upload operations...`);
            await Promise.all(allUploadPromises);
            console.log('🎉 [CREATE] All uploads completed successfully');
          } catch (error) {
            console.error('❌ [CREATE] Error during uploads:', error);
            // Không throw error để không block việc tạo sản phẩm
            // Sản phẩm đã được tạo thành công, chỉ upload content bị lỗi
          }
        } else {
          console.log('ℹ️ [CREATE] No uploads needed');
        }

        console.log('✅ [CREATE] Product creation process completed');
      }

      setSelectedProduct(null);
      hideForm();
      // Navigate về list sau khi submit thành công
      if (productId) {
        navigate('/marketplace/products-for-sale');
      }
    } catch (error) {
      console.error('Error submitting product:', error);
      // Error đã được handle trong mutation hooks
    }
  };

  // Xử lý hủy form
  const handleCancel = () => {
    try {
      console.log('🔍 [CANCEL] Canceling form...');

      // Reset mutations nếu đang pending
      if (createProductMutation.isPending) {
        console.log('⚠️ [CANCEL] Create mutation is pending, resetting...');
        createProductMutation.reset();
      }

      if (updateProductMutation.isPending) {
        console.log('⚠️ [CANCEL] Update mutation is pending, resetting...');
        updateProductMutation.reset();
      }

      setSelectedProduct(null);
      hideAddForm();
      hideForm();

      // Navigate về list nếu đang ở edit URL
      if (productId) {
        navigate('/marketplace/products-for-sale');
      }

      console.log('✅ [CANCEL] Form canceled successfully');
    } catch (error) {
      console.error('❌ [CANCEL] Error during cancel:', error);
      // Vẫn thực hiện cancel dù có lỗi
      setSelectedProduct(null);
      hideForm();
      if (productId) {
        navigate('/marketplace/products-for-sale');
      }
    }
  };




  // const handleConfirmDelete = async () => {
  //   if (productToDelete) {
  //     try {
  //       console.log('🔍 [ProductsForSalePage] Starting delete for product:', productToDelete);
  //       await deleteProductMutation.mutateAsync(productToDelete.id);
  //       console.log('✅ [ProductsForSalePage] Delete completed successfully');
  //       setProductToDelete(null);
  //       setShowDeleteConfirm(false);

  //       // Force refetch để đảm bảo data được cập nhật
  //       console.log('🔍 [ProductsForSalePage] Forcing refetch...');
  //       await refetch();
  //       console.log('✅ [ProductsForSalePage] Refetch completed');
  //     } catch (error) {
  //       console.error('❌ [ProductsForSalePage] Error deleting product:', error);
  //       // Error đã được handle trong mutation hooks
  //     }
  //   }
  // };

  // Xử lý gửi sản phẩm để duyệt
  const handleSubmitForApproval = async (product: ApiProduct) => {
    try {
      await submitForApprovalMutation.mutateAsync(product.id);
    } catch (error) {
      console.error('Error submitting product for approval:', error);
      // Error đã được handle trong mutation hooks
    }
  };

  // Xử lý hủy gửi duyệt
  const handleCancelSubmission = async (product: ApiProduct) => {
    try {
      await cancelSubmissionMutation.mutateAsync(product.id);
    } catch (error) {
      console.error('Error canceling product submission:', error);
      // Error đã được handle trong mutation hooks
    }
  };





  // Xử lý xác nhận xóa nhiều
  const handleConfirmBulkDelete = async () => {
    try {
      await batchDeleteMutation.mutateAsync(selectedRowKeys);
      setSelectedRowKeys([]);
      setShowBulkDeleteConfirm(false);
    } catch (error) {
      console.error('Error batch deleting products:', error);
      // Error đã được handle trong mutation hooks
    }
  };

  // Xử lý hủy xóa nhiều
  const handleCancelBulkDelete = () => {
    setShowBulkDeleteConfirm(false);
  };



  // Hiển thị loading
  if (isLoadingProducts || isLoadingDetail) {
    return (
      <div className="flex justify-center items-center h-64">
        <Loading />
      </div>
    );
  }



  // Hiển thị lỗi
  if (error) {
    return (
      <Alert
        type="error"
        title={t('marketplace:productsForSale.error.loadDataTitle', 'Lỗi tải dữ liệu')}
        message={error.message}
        action={
          <Button variant="outline" onClick={() => refetch()}>
            {t('marketplace:productsForSale.error.retry', 'Thử lại')}
          </Button>
        }
      />
    );
  }

  return (
    <div>
      <MenuIconBar
        onSearch={dataTable.tableData.handleSearch}
        onAdd={handleAdd}
        items={dataTable.menuItems}
        onColumnVisibilityChange={dataTable.columnVisibility.setVisibleColumns}
        columns={dataTable.columnVisibility.visibleColumns}
        showDateFilter={false}
        showColumnFilter={true}
        additionalIcons={[
          {
            icon: 'trash',
            tooltip: t('marketplace:bulkActions.bulkDelete', 'Xóa nhiều'),
            variant: 'primary',
            onClick: () => {
              if (selectedRowKeys.length > 0 && (userProductsData?.items?.length ?? 0) > 0) {
                setShowBulkDeleteConfirm(true);
              } else {
                NotificationUtil.info({
                  message: t(
                    'marketplace:bulkActions.selectProductsToDelete',
                    'Vui lòng chọn ít nhất một sản phẩm để xóa'
                  ),
                  duration: 3000,
                });
              }
            },
            className: 'text-red-500',
            condition: selectedRowKeys.length > 0 && (userProductsData?.items?.length ?? 0) > 0,
          },
        ]}
      />

      {/* Hiển thị ActiveFilters */}
      <div className="mb-4">
        {(dataTable.tableData.searchTerm ||
          (dataTable.filter.selectedValue && dataTable.filter.selectedValue !== 'all') ||
          dataTable.tableData.sortBy) && (
          <div className="mb-4">
            <div className="flex flex-wrap gap-2">
              {/* Search filter */}
              {dataTable.tableData.searchTerm && (
                <div className="flex items-center bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm">
                  <span className="mr-2">
                    {t('common:search', 'Tìm kiếm')}: "{dataTable.tableData.searchTerm}"
                  </span>
                  <button
                    onClick={handleClearSearch}
                    className="ml-1 hover:bg-blue-200 rounded-full p-1"
                  >
                    <Icon name="x" size="xs" />
                  </button>
                </div>
              )}

              {/* Status filter */}
              {dataTable.filter.selectedValue && dataTable.filter.selectedValue !== 'all' && (
                <div className="flex items-center bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm">
                  <span className="mr-2">
                    {t('marketplace:productsForSale.table.status', 'Trạng thái')}: {getFilterLabel()}
                  </span>
                  <button
                    onClick={handleClearFilter}
                    className="ml-1 hover:bg-green-200 rounded-full p-1"
                  >
                    <Icon name="x" size="xs" />
                  </button>
                </div>
              )}

              {/* Sort filter */}
              {dataTable.tableData.sortBy && (
                <div className="flex items-center bg-purple-100 text-purple-800 px-3 py-1 rounded-full text-sm">
                  <span className="mr-2">
                    {t('common:sortBy', 'Sắp xếp')}: {dataTable.tableData.sortBy} ({dataTable.tableData.sortDirection})
                  </span>
                  <button
                    onClick={handleClearSort}
                    className="ml-1 hover:bg-purple-200 rounded-full p-1"
                  >
                    <Icon name="x" size="xs" />
                  </button>
                </div>
              )}

              {/* Clear all button */}
              <button
                onClick={handleClearAll}
                className="flex items-center bg-gray-100 text-gray-800 px-3 py-1 rounded-full text-sm hover:bg-gray-200"
              >
                <Icon name="x" size="xs" className="mr-1" />
                {t('common:clearAll', 'Xóa tất cả')}
              </button>
            </div>
          </div>
        )}
      </div>

        {/* Add Product Form */}
      <SlideInForm isVisible={isAddFormVisible}>
        <AddProductForm
          onSubmit={handleAddProductSubmit}
          onCancel={handleCancel}
          onSuccess={handleAddProductSuccess}
        />
      </SlideInForm>

      <SlideInForm isVisible={isVisible}>
        {selectedProduct ? (
          <ProductForSaleForm
            onSubmit={handleSubmit}
            onCancel={handleCancel}
            isSubmitting={createProductMutation.isPending || updateProductMutation.isPending}
            initialValues={{
              name: selectedProduct.name,
              description: selectedProduct.description,
              images: selectedProduct.images && selectedProduct.images.length > 0
                ? selectedProduct.images.map(img =>
                    typeof img === 'string' ? img : img?.url || ''
                  ).filter(Boolean)
                : [],
              oldImageKeys: selectedProduct.images && selectedProduct.images.length > 0
                ? (() => {
                    console.log('🔍 Processing selectedProduct.images:', selectedProduct.images);
                    const validImages = selectedProduct.images.filter(img => {
                      const isValid = img && typeof img === 'object' && img.key;
                      console.log('🔍 Image:', img, 'isValid:', isValid);
                      return isValid;
                    });
                    const keys = validImages.map(img => img.key).filter(Boolean) as string[];
                    console.log('🔍 Extracted keys:', keys);
                    return keys;
                  })()
                : [],
              price: selectedProduct.discountedPrice || 0,
              category: selectedProduct.category, // Use as-is, form will handle type compatibility
              detail: selectedProduct.detail || '', // Existing detail content
              userManual: selectedProduct.userManual || '', // Existing userManual content
            } as unknown as Partial<ProductForSaleFormValues & { oldImageKeys?: string[]; images?: string[]; detail?: string; userManual?: string }>}
          />
        ) : (
          <ProductForSaleForm
            onSubmit={handleSubmit}
            onCancel={handleCancel}
            isSubmitting={createProductMutation.isPending || updateProductMutation.isPending}
          />
        )}
      </SlideInForm>




      <Card className="overflow-hidden">
        <Table<ApiProduct>
          columns={dataTable.columnVisibility.visibleTableColumns}
          data={userProductsData?.items || []}
          rowKey="id"
          loading={isLoadingProducts}
          sortable={true}
          selectable={true}
          rowSelection={{
            selectedRowKeys,
            onChange: keys => setSelectedRowKeys(keys.map(key => Number(key)).filter(key => !isNaN(key))),
          }}
          onSortChange={dataTable.tableData.handleSortChange}
          pagination={{
            current: userProductsData?.meta.currentPage || 1,
            pageSize: dataTable.tableData.pageSize,
            total: userProductsData?.meta.totalItems || 0,
            onChange: dataTable.tableData.handlePageChange,
            showSizeChanger: true,
            pageSizeOptions: [10, 20, 50, 100],
            showFirstLastButtons: true,
            showPageInfo: true,
          }}
        />
      </Card>

      {/* Confirmation Modal for Bulk Delete */}
      <Modal
        isOpen={showBulkDeleteConfirm}
        onClose={handleCancelBulkDelete}
        title={t('marketplace:bulkActions.confirmBulkDelete', 'Xác nhận xóa nhiều')}
        size="sm"
        footer={
          <div className="flex justify-end space-x-3">
            <Button
              variant="outline"
              onClick={handleCancelBulkDelete}
              disabled={batchDeleteMutation.isPending}
            >
              {t('common:cancel', 'Hủy')}
            </Button>
            <Button
              variant="danger"
              onClick={handleConfirmBulkDelete}
              isLoading={batchDeleteMutation.isPending}
            >
              {t('common:delete', 'Xóa')}
            </Button>
          </div>
        }
      >
        <div className="p-6">
          <div className="flex items-center mb-4">
            <div className="flex-shrink-0 w-10 h-10 mx-auto bg-red-100 rounded-full flex items-center justify-center">
              <Icon name="trash" size="md" className="text-red-600" />
            </div>
            <div className="ml-4">
              <Typography variant="h6">
                {t('marketplace:bulkActions.confirmBulkDelete', 'Xác nhận xóa nhiều')}
              </Typography>
              <Typography variant="body2">
                {t(
                  'marketplace:bulkActions.confirmBulkDeleteMessage',
                  'Bạn có chắc chắn muốn xóa {{count}} sản phẩm đã chọn?',
                  { count: selectedRowKeys.length }
                )}
              </Typography>
            </div>
          </div>
        </div>
      </Modal>
    </div>
  );
};

export default ProductsForSalePage;
