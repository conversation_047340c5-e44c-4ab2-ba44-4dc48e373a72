import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON> } from '@/shared/components/common';
import { AsyncSelectWithPagination } from '@/shared/components/common/Select';
import {
  useUserKeyLLMList,
  useUserModelsByKey,
  useActiveSystemModels,
  useUserFineTuneModels,
} from '../user-mode-base/hooks/useUserModeBase';
import ModelGrid from '../components/ModelGrid';
import Pagination from '@/shared/components/common/Pagination';

type TabType = 'user-keys' | 'user-models' | 'system-models' | 'fine-tune-models';

const ApiIntegrationPage = () => {
  const { t } = useTranslation();

  // State cho tab hiện tại
  const [activeTab, setActiveTab] = useState<TabType>('system-models');

  // State cho User Models by Key
  const [selectedKeyId, setSelectedKeyId] = useState<string>('');

  // State for system models pagination
  const [systemPage, setSystemPage] = useState(1);
  const [systemLimit, setSystemLimit] = useState(10);

  // State for fine-tune models pagination
  const [fineTunePage, setFineTunePage] = useState(1);
  const [fineTuneLimit, setFineTuneLimit] = useState(10);

  // State for user models pagination
  const [userModelPage, setUserModelPage] = useState(1);
  const [userModelLimit, setUserModelLimit] = useState(10);

  // React Query hooks
  const userKeysQuery = useUserKeyLLMList({ page: 1, limit: 100 });
  const userModelsQuery = useUserModelsByKey(selectedKeyId);
  const systemModelsQuery = useActiveSystemModels(['OPENAI']);
  const fineTuneModelsQuery = useUserFineTuneModels();

  // Extract data from queries
  const userKeys = userKeysQuery.data?.items || [];
  const userModels = userModelsQuery.data?.items || [];
  const systemModels = systemModelsQuery.data?.items || [];
  const fineTuneModels = fineTuneModelsQuery.data?.items || [];

  // Loading states
  const loading =
    userKeysQuery.isLoading ||
    userModelsQuery.isLoading ||
    systemModelsQuery.isLoading ||
    fineTuneModelsQuery.isLoading;

  // Handle tab change
  const handleTabChange = (tab: TabType) => {
    setActiveTab(tab);
  };

  // Handle key selection for User Models tab
  const handleKeySelection = (keyId: string) => {
    setSelectedKeyId(keyId);
  };

  // Calculate system models pagination
  const systemTotal = systemModels.length;
  const systemTotalPages = Math.ceil(systemTotal / systemLimit) || 1;
  const systemModelsPage = systemModels.slice(
    (systemPage - 1) * systemLimit,
    systemPage * systemLimit
  );

  // Calculate user models pagination
  const userModelsTotal = userModels.length;
  const userModelsTotalPages = Math.ceil(userModelsTotal / userModelLimit) || 1;
  const userModelsPage = userModels.slice(
    (userModelPage - 1) * userModelLimit,
    userModelPage * userModelLimit
  );

  // Calculate fine-tune models pagination
  const fineTuneTotal = fineTuneModels.length;
  const fineTuneTotalPages = Math.ceil(fineTuneTotal / fineTuneLimit) || 1;
  const fineTuneModelsPage = fineTuneModels.slice(
    (fineTunePage - 1) * fineTuneLimit,
    fineTunePage * fineTuneLimit
  );

  return (
    <div className="container mx-auto p-6">
      {/* Tab Navigation */}
      <div className="flex space-x-2 mb-6 border-b">
        <Button
          variant={activeTab === 'system-models' ? 'primary' : 'ghost'}
          onClick={() => handleTabChange('system-models')}
          className="rounded-b-none"
        >
          {t('user-dataset:apiIntegration.tabs.systemModels')}
        </Button>
        <Button
          variant={activeTab === 'user-models' ? 'primary' : 'ghost'}
          onClick={() => handleTabChange('user-models')}
          className="rounded-b-none"
        >
          {t('user-dataset:apiIntegration.tabs.userModels')}
        </Button>
        <Button
          variant={activeTab === 'fine-tune-models' ? 'primary' : 'ghost'}
          onClick={() => handleTabChange('fine-tune-models')}
          className="rounded-b-none"
        >
          {t('user-dataset:apiIntegration.tabs.fineTuneModels')}
        </Button>
      </div>

      {/* Content Area */}
      <Card className="p-6">
        {loading && (
          <div className="flex justify-center items-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
            <span className="ml-2">{t('user-dataset:apiIntegration.loading.loadingData')}</span>
          </div>
        )}

        {/* Empty States */}
        {!loading && activeTab === 'user-keys' && userKeys.length === 0 && (
          <div className="text-center py-8">
            <Typography variant="body1" className="text-muted-foreground">
              {t('user-dataset:apiIntegration.messages.noUserKeys')}
            </Typography>
          </div>
        )}

        {!loading && activeTab === 'user-models' && selectedKeyId && userModels.length === 0 && (
          <div className="text-center py-8">
            <Typography variant="body1" className="text-muted-foreground">
              {t('user-dataset:apiIntegration.messages.noModelsForKey')}
            </Typography>
          </div>
        )}

        {!loading && activeTab === 'system-models' && systemModels.length === 0 && (
          <div className="text-center py-8">
            <Typography variant="body1" className="text-muted-foreground">
              {t('user-dataset:apiIntegration.messages.noSystemModels')}
            </Typography>
          </div>
        )}

        {activeTab === 'user-models' && (
          <>
            <div className="mb-4">
              <label className="block text-sm font-medium mb-2">
                {t('user-dataset:apiIntegration.form.selectUserKey')}
              </label>
              <AsyncSelectWithPagination
                value={selectedKeyId}
                onChange={value => handleKeySelection(value as string)}
                loadOptions={async ({ page, limit }) => {
                  const startIndex = ((page || 1) - 1) * (limit || 20);
                  const endIndex = startIndex + (limit || 20);
                  const paginatedKeys = userKeys.slice(startIndex, endIndex);

                  const items = paginatedKeys.map(key => ({
                    value: key.id,
                    label: key.name || `Key ${key.id}`,
                    data: key as Record<string, unknown>,
                  }));

                  return {
                    items,
                    totalItems: userKeys.length,
                    totalPages: Math.ceil(userKeys.length / (limit || 20)),
                    currentPage: page || 1,
                  };
                }}
                placeholder={t('user-dataset:apiIntegration.placeholders.selectUserKey')}
                fullWidth
                size="md"
                autoLoadInitial
                itemsPerPage={20}
                className="max-w-md"
              />
            </div>

            {selectedKeyId && (
              <>
                <ModelGrid models={userModelsPage} loading={loading} />
                {userModelsTotal > 0 && (
                  <div className="flex justify-end mt-8">
                    <Pagination
                      variant="simple"
                      currentPage={userModelPage}
                      itemsPerPage={userModelLimit}
                      totalPages={userModelsTotalPages}
                      onPageChange={setUserModelPage}
                      onItemsPerPageChange={newLimit => {
                        setUserModelLimit(newLimit);
                        setUserModelPage(1);
                      }}
                      showFirstLastButtons={false}
                      showItemsPerPageSelector={true}
                      showPageInfo={false}
                      itemsPerPageOptions={[10, 20, 50, 100]}
                      size="md"
                      borderless={true}
                    />
                  </div>
                )}
              </>
            )}
          </>
        )}

        {activeTab === 'system-models' && (
          <>
            <ModelGrid models={systemModelsPage} loading={loading} />
            {systemTotal > 0 && (
              <div className="flex justify-end mt-8">
                <Pagination
                  variant="simple"
                  currentPage={systemPage}
                  itemsPerPage={systemLimit}
                  totalPages={systemTotalPages}
                  onPageChange={setSystemPage}
                  onItemsPerPageChange={newLimit => {
                    setSystemLimit(newLimit);
                    setSystemPage(1);
                  }}
                  showFirstLastButtons={false}
                  showItemsPerPageSelector={true}
                  showPageInfo={false}
                  itemsPerPageOptions={[10, 20, 50, 100]}
                  size="md"
                  borderless={true}
                />
              </div>
            )}
          </>
        )}

        {activeTab === 'fine-tune-models' && (
          <>
            <ModelGrid models={fineTuneModelsPage} loading={loading} />
            {fineTuneTotal > 0 && (
              <div className="flex justify-end mt-8">
                <Pagination
                  variant="simple"
                  currentPage={fineTunePage}
                  itemsPerPage={fineTuneLimit}
                  totalPages={fineTuneTotalPages}
                  onPageChange={setFineTunePage}
                  onItemsPerPageChange={newLimit => {
                    setFineTuneLimit(newLimit);
                    setFineTunePage(1);
                  }}
                  showFirstLastButtons={false}
                  showItemsPerPageSelector={true}
                  showPageInfo={false}
                  itemsPerPageOptions={[10, 20, 50, 100]}
                  size="md"
                  borderless={true}
                />
              </div>
            )}
          </>
        )}
      </Card>
    </div>
  );
};

export default ApiIntegrationPage;
