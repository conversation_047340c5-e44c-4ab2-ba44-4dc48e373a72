import { useMutation, useQueryClient } from '@tanstack/react-query';
import { toggleAgentActive } from '../api/agent.api';
import { AGENT_QUERY_KEYS } from '../constants/agent-query-keys';
import { AgentListItemDto } from '../api/agent.api';
import { ApiResponseDto } from '@/shared/dto/response/api-response.dto';
import { PaginatedResult } from '@/shared/dto/response/paginated-result.dto';
import { AgentDetailDto } from '../types/dto';

/**
 * Hook tối ưu cho việc toggle agent active với optimistic update
 * Cập nhật UI ngay lập tức và chỉ rollback nếu API thất bại
 */
export const useOptimisticToggleAgent = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationKey: [AGENT_QUERY_KEYS.TOGGLE_AGENT_ACTIVE],
    mutationFn: (id: string) => toggleAgentActive(id),
    
    // Optimistic update - cập nhật UI ngay lập tức
    onMutate: async (id: string) => {
      // Cancel any outgoing refetches để tránh overwrite optimistic update
      await queryClient.cancelQueries({ queryKey: [AGENT_QUERY_KEYS.AGENT_LIST] });
      await queryClient.cancelQueries({ queryKey: [AGENT_QUERY_KEYS.AGENT_DETAIL, id] });

      // Snapshot previous values để rollback nếu cần
      const previousAgentList = queryClient.getQueryData<ApiResponseDto<PaginatedResult<AgentListItemDto>>>([AGENT_QUERY_KEYS.AGENT_LIST]);
      const previousAgentDetail = queryClient.getQueryData([AGENT_QUERY_KEYS.AGENT_DETAIL, id]);

      // Optimistically update agent list
      queryClient.setQueryData<ApiResponseDto<PaginatedResult<AgentListItemDto>>>(
        [AGENT_QUERY_KEYS.AGENT_LIST], 
        (old) => {
          if (!old?.result?.items) return old;
          
          return {
            ...old,
            result: {
              ...old.result,
              items: old.result.items.map((agent: AgentListItemDto) =>
                agent.id === id ? { ...agent, active: !agent.active } : agent
              ),
            },
          };
        }
      );

      // Optimistically update agent detail if exists
      queryClient.setQueryData<ApiResponseDto<AgentDetailDto>>([AGENT_QUERY_KEYS.AGENT_DETAIL, id], (old) => {
        if (!old?.result) return old;

        return {
          ...old,
          result: {
            ...old.result,
            active: !old.result.active,
          },
        };
      });

      // Return context object với previous values
      return { previousAgentList, previousAgentDetail, id };
    },

    // Nếu mutation thất bại, rollback optimistic update
    onError: (_, id, context) => {
      if (context?.previousAgentList) {
        queryClient.setQueryData([AGENT_QUERY_KEYS.AGENT_LIST], context.previousAgentList);
      }
      if (context?.previousAgentDetail) {
        queryClient.setQueryData([AGENT_QUERY_KEYS.AGENT_DETAIL, id], context.previousAgentDetail);
      }
    },

    // Chỉ invalidate nếu cần thiết (ví dụ: để sync với server state)
    onSettled: () => {
      // Có thể bỏ qua invalidate để tối ưu hơn nữa
      // queryClient.invalidateQueries({ queryKey: [AGENT_QUERY_KEYS.AGENT_LIST] });
    },
  });
};

/**
 * Hook đơn giản hơn cho toggle agent - chỉ cập nhật sau khi API thành công
 * Sử dụng khi muốn đảm bảo data consistency 100%
 */
export const useSimpleToggleAgent = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationKey: [AGENT_QUERY_KEYS.TOGGLE_AGENT_ACTIVE],
    mutationFn: (id: string) => toggleAgentActive(id),
    
    // Chỉ cập nhật sau khi API thành công
    onSuccess: (_, id) => {
      // Cập nhật cache một cách selective
      queryClient.setQueryData<ApiResponseDto<PaginatedResult<AgentListItemDto>>>(
        [AGENT_QUERY_KEYS.AGENT_LIST], 
        (old) => {
          if (!old?.result?.items) return old;
          
          return {
            ...old,
            result: {
              ...old.result,
              items: old.result.items.map((agent: AgentListItemDto) =>
                agent.id === id ? { ...agent, active: !agent.active } : agent
              ),
            },
          };
        }
      );

      // Cập nhật agent detail nếu có
      queryClient.setQueryData<ApiResponseDto<AgentDetailDto>>([AGENT_QUERY_KEYS.AGENT_DETAIL, id], (old) => {
        if (!old?.result) return old;

        return {
          ...old,
          result: {
            ...old.result,
            active: !old.result.active,
          },
        };
      });
    },
  });
};
