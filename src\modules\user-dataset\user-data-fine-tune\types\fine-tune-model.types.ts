/**
 * Types cho Fine-tune Model Creation
 */

export interface CreateFineTuneModelDto {
  /**
   * Tên model fine-tune
   */
  name: string;

  /**
   * Mô tả model
   */
  description: string;

  /**
   * ID của dataset để fine-tune
   */
  datasetId: string;

  /**
   * ID của base model
   */
  baseModelId: string;

  /**
   * Nhà cung cấp
   */
  provider: string;

  /**
   * Suffix cho model
   */
  suffix: string;

  /**
   * Cấu hình hyperparameters
   */
  hyperparameters: HyperParameters;
}

export interface HyperParameters {
  /**
   * Số epochs
   */
  epochs: number;

  /**
   * Batch size
   */
  batchSize: string | number;

  /**
   * Learning rate
   */
  learningRate: number;
}

export interface HyperParameterConfig {
  /**
   * Chế độ cấu hình: auto hoặc custom
   */
  mode: 'auto' | 'custom';

  /**
   * Cấu hình custom (chỉ khi mode = 'custom')
   */
  customConfig?: HyperParameters;
}

/**
 * Response khi tạo fine-tune model thành công
 */
export interface CreateFineTuneModelResponse {
  /**
   * ID của model đã tạo
   */
  id: string;

  /**
   * Thông báo
   */
  message: string;

  /**
   * Trạng thái
   */
  status: string;
}
