import { apiClient } from '@/shared/api/axios';
import {
  CreateFineTuneModelDto,
  CreateFineTuneModelResponse,
} from '../user-data-fine-tune/types/fine-tune-model.types';

const API_BASE_URL = '/user/fine-tuning-jobs';

/**
 * Tạo mới fine-tune model
 * @param data Dữ liệu để tạo fine-tune model
 * @returns Thông tin model đã tạo
 */
export const createFineTuneModel = async (
  data: CreateFineTuneModelDto
): Promise<CreateFineTuneModelResponse> => {
  const response = await apiClient.post<CreateFineTuneModelResponse>(API_BASE_URL, data);
  return response.result;
};

/**
 * Lấy danh sách fine-tune models
 * @returns Danh sách fine-tune models
 */
export const getFineTuneModels = async (): Promise<CreateFineTuneModelResponse[]> => {
  const response = await apiClient.get<CreateFineTuneModelResponse[]>(API_BASE_URL);
  return response.result;
};

/**
 * L<PERSON>y chi tiết fine-tune model
 * @param id ID của model
 * @returns Thông tin chi tiết model
 */
export const getFineTuneModelDetail = async (id: string): Promise<CreateFineTuneModelResponse> => {
  const response = await apiClient.get<CreateFineTuneModelResponse>(`${API_BASE_URL}/${id}`);
  return response.result;
};

/**
 * Xóa fine-tune model
 * @param id ID của model
 * @returns Kết quả xóa
 */
export const deleteFineTuneModel = async (id: string): Promise<{ success: boolean }> => {
  const response = await apiClient.delete<{ success: boolean }>(`${API_BASE_URL}/${id}`);
  return response.result;
};
