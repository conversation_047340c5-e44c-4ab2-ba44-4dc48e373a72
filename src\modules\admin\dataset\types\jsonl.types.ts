/**
 * Interface cho JSONL dataset format
 */

// Tool call interfaces
export interface ToolCallFunction {
  name: string;
  arguments: string;
}

export interface ToolCall {
  id: string;
  type: string;
  function: ToolCallFunction;
}

// Content types for messages
export interface TextContent {
  type: 'text';
  text: string;
}

export interface ImageUrlContent {
  type: 'image_url';
  image_url: {
    url: string;
  };
}

export type MessageContent = string | (TextContent | ImageUrlContent)[];

// Message interface với đầy đủ thuộc tính
export interface Message {
  role: 'system' | 'user' | 'assistant' | 'tool';
  content: MessageContent;
  tool_calls?: ToolCall[];
  tool_call_id?: string;
  weight?: number;
}

// Tool function parameter properties
export interface ToolFunctionProperty {
  type: string;
  description?: string;
  enum?: string[];
  items?: {
    type: string;
    enum?: string[];
  };
  required?: string[];
  properties?: Record<string, ToolFunctionProperty>;
  additionalProperties?: boolean;
  minimum?: number;
  maximum?: number;
  exclusiveMinimum?: number;
}

// Tool function parameters
export interface ToolFunctionParameters {
  type: string;
  required: string[];
  properties: Record<string, ToolFunctionProperty>;
  additionalProperties?: boolean;
}

// Tool function definition
export interface ToolFunction {
  name: string;
  description: string;
  parameters: ToolFunctionParameters;
}

// Tool definition
export interface Tool {
  type: 'function';
  function: ToolFunction;
}

// Main conversation structure
export interface JsonlConversation {
  messages: Message[];
  tools?: Tool[];
}

// For backward compatibility
export interface Messages {
  messages: Message[];
}

export interface JsonlLine {
  [key: string]: Messages;
}

/**
 * Interface cho upload URL response
 */
export interface UploadUrlResponse {
  uploadUrl: string;
  viewUrl: string;
}

/**
 * Interface cho dataset creation payload
 */
export interface DatasetCreationPayload {
  trainData: JsonlConversation[];
  validData: JsonlConversation[];
  name?: string;
  description?: string;
  provider?: string;
}

/**
 * Utility functions để convert data
 */
import {
  ImportedConversation,
  DatasetMessage,
} from '../user-data-fine-tune/types/user-data-fine-tune.types';

/**
 * Convert DatasetMessage to Message format
 */
export const convertDatasetMessageToMessage = (datasetMessage: DatasetMessage): Message => {
  return {
    role: datasetMessage.role,
    content: datasetMessage.content,
    ...(datasetMessage.weight !== undefined && { weight: datasetMessage.weight }),
  };
};

/**
 * Convert ImportedConversation to JsonlConversation format
 */
export const convertConversationToJsonlConversation = (
  conversation: ImportedConversation
): JsonlConversation => {
  return {
    messages: conversation.messages.map(convertDatasetMessageToMessage),
    // tools sẽ được thêm vào nếu cần thiết
  };
};

/**
 * Convert ImportedConversation to Messages format (backward compatibility)
 */
export const convertConversationToMessages = (conversation: ImportedConversation): Messages => {
  return {
    messages: conversation.messages.map(convertDatasetMessageToMessage),
  };
};

/**
 * Convert array of ImportedConversation to array of JsonlConversation
 */
export const convertConversationsToJsonlArray = (
  conversations: ImportedConversation[]
): JsonlConversation[] => {
  return conversations.map(convertConversationToJsonlConversation);
};

/**
 * Convert array of ImportedConversation to array of Messages (backward compatibility)
 */
export const convertConversationsToMessagesArray = (
  conversations: ImportedConversation[]
): Messages[] => {
  return conversations.map(convertConversationToMessages);
};

/**
 * Create dataset payload from training and validation conversations
 */
export const createDatasetPayload = (
  trainingConversations: ImportedConversation[],
  validationConversations: ImportedConversation[],
  additionalData?: {
    name?: string;
    description?: string;
    provider?: string;
  }
): DatasetCreationPayload => {
  return {
    trainData: convertConversationsToJsonlArray(trainingConversations),
    validData: convertConversationsToJsonlArray(validationConversations),
    ...additionalData,
  };
};

/**
 * Parse JSONL line to JsonlConversation
 */
export const parseJsonlLine = (line: string): JsonlConversation => {
  try {
    const parsed = JSON.parse(line);
    return parsed as JsonlConversation;
  } catch (error) {
    throw new Error(`Invalid JSONL format: ${error}`);
  }
};

/**
 * Convert JsonlConversation to JSONL string
 */
export const toJsonlString = (conversation: JsonlConversation): string => {
  return JSON.stringify(conversation);
};

/**
 * Create text content
 */
export const createTextContent = (text: string): TextContent => {
  return {
    type: 'text',
    text,
  };
};

/**
 * Create image content
 */
export const createImageContent = (url: string): ImageUrlContent => {
  return {
    type: 'image_url',
    image_url: {
      url,
    },
  };
};

/**
 * Create mixed content with text and image
 */
export const createMixedContent = (text?: string, imageUrl?: string): MessageContent => {
  const content: (TextContent | ImageUrlContent)[] = [];

  if (text && text.trim()) {
    content.push(createTextContent(text));
  }

  if (imageUrl) {
    content.push(createImageContent(imageUrl));
  }

  if (content.length === 1 && content[0]?.type === 'text') {
    return text || '';
  }

  return content;
};

/**
 * Extract text from message content
 */
export const extractTextFromContent = (content: MessageContent): string => {
  if (typeof content === 'string') {
    return content;
  }

  const textContent = content.find(item => item.type === 'text') as TextContent;
  return textContent?.text || '';
};

/**
 * Extract image URLs from message content
 */
export const extractImageUrlsFromContent = (content: MessageContent): string[] => {
  if (typeof content === 'string') {
    return [];
  }

  return content
    .filter(item => item.type === 'image_url')
    .map(item => (item as ImageUrlContent).image_url.url);
};
