import React, { useState, useMemo, useRef, useCallback } from 'react';
import { ProductDetail } from '../types/product.types';
import { Icon } from '@/shared/components/common';

// CSS để ẩn thanh cuộn
const hideScrollbarCSS = `
  .hide-scrollbar::-webkit-scrollbar {
    display: none;
  }
  .hide-scrollbar {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }
`;

// Đ<PERSON>nh nghĩa kiểu dữ liệu cho media
interface MediaItem {
  id: string;
  type: 'image' | 'video';
  src: string;
  alt: string;
  width: number;
  height: number;
  thumbnail?: string;
}

// Danh sách hình ảnh test
const TEST_IMAGES = [
  'https://images.unsplash.com/photo-1505740420928-5e560c06d30e?q=80&w=1000',
  'https://images.unsplash.com/photo-1523275335684-37898b6baf30?q=80&w=1000',
  'https://images.unsplash.com/photo-1542291026-7eec264c27ff?q=80&w=1000',
  'https://images.unsplash.com/photo-1560343090-f0409e92791a?q=80&w=1000',
  'https://images.unsplash.com/photo-1546868871-7041f2a55e12?q=80&w=1000',
];

// Video test
const TEST_VIDEOS = [
  'https://www.w3schools.com/html/mov_bbb.mp4',
  'https://www.w3schools.com/html/movie.mp4',
];

interface ProductGalleryProps {
  /**
   * Thông tin sản phẩm
   */
  product: ProductDetail;

  /**
   * Sử dụng hình ảnh test thay vì hình ảnh từ sản phẩm
   */
  useTestImages?: boolean;

  /**
   * Sử dụng video test
   */
  useTestVideos?: boolean;

  /**
   * Class bổ sung
   */
  className?: string;
}

/**
 * Component hiển thị gallery hình ảnh và video sản phẩm
 * Hiển thị một hình ảnh/video chính ở trên và danh sách media phía dưới
 */
const ProductGallery: React.FC<ProductGalleryProps> = ({
  product,
  useTestImages = false,
  useTestVideos = false,
  className = '',
}) => {
  // State để lưu media đang được chọn
  const [selectedMediaIndex, setSelectedMediaIndex] = useState(0);
  // State để kiểm soát việc hiển thị lightbox
  const [isLightboxOpen, setIsLightboxOpen] = useState(false);
  // Ref cho container của thumbnails
  const thumbnailsContainerRef = useRef<HTMLDivElement>(null);

  // Tạo danh sách media cho gallery
  const galleryMedia: MediaItem[] = useMemo(() => {
    const mediaItems: MediaItem[] = [];

    if (useTestImages) {
      // Thêm hình ảnh test
      TEST_IMAGES.forEach((image, index) => {
        mediaItems.push({
          id: `test-image-${index}`,
          type: 'image',
          src: image,
          alt: `${product.name} - Hình ${index + 1}`,
          width: 800,
          height: 800,
        });
      });
    } else {
      // Chỉ sử dụng hình ảnh từ API, không thêm thumbnail riêng
      console.log('🔍 [ProductGallery] Using real images from product:', product.images);

      if (product.images && product.images.length > 0) {
        product.images.forEach((image, index) => {
          mediaItems.push({
            id: `image-${index}`,
            type: 'image',
            src: image,
            alt: `${product.name} - Hình ${index + 1}`,
            width: 800,
            height: 800,
          });
        });
      } else {
        // Fallback nếu không có hình ảnh
        mediaItems.push({
          id: 'placeholder',
          type: 'image',
          src: product.thumbnail || '/placeholder-image.jpg',
          alt: product.name,
          width: 800,
          height: 800,
        });
      }

      console.log('🔍 [ProductGallery] Final media items:', mediaItems);
    }

    // Thêm video test nếu được yêu cầu
    if (useTestVideos) {
      TEST_VIDEOS.forEach((video, index) => {
        mediaItems.push({
          id: `test-video-${index}`,
          type: 'video',
          src: video,
          alt: `${product.name} - Video ${index + 1}`,
          width: 800,
          height: 450,
          thumbnail: TEST_IMAGES[0], // Sử dụng hình ảnh đầu tiên làm thumbnail cho video
        });
      });
    }

    return mediaItems;
  }, [product, useTestImages, useTestVideos]);

  // Xử lý khi click vào thumbnail
  const handleThumbnailClick = (index: number) => {
    setSelectedMediaIndex(index);
  };

  // Xử lý cuộn ngang cho thumbnails
  const handleScroll = (direction: 'left' | 'right') => {
    if (thumbnailsContainerRef.current) {
      const container = thumbnailsContainerRef.current;
      const scrollAmount = 200; // Số pixel cuộn mỗi lần

      if (direction === 'left') {
        container.scrollBy({ left: -scrollAmount, behavior: 'smooth' });
      } else {
        container.scrollBy({ left: scrollAmount, behavior: 'smooth' });
      }
    }
  };

  // Lấy media hiện tại
  const currentMedia = galleryMedia[selectedMediaIndex];

  // Early return nếu không có media
  if (!currentMedia) {
    return null;
  }

  // Không cần chuyển đổi MediaItem sang GalleryImage vì chúng ta đang hiển thị hình ảnh hiện tại trực tiếp

  // Không cần sắp xếp lại galleryImages vì chúng ta đang hiển thị hình ảnh hiện tại trực tiếp

  // Xử lý khi click vào hình ảnh chính
  const handleMainImageClick = () => {
    console.log('Image clicked!');
    if (currentMedia.type === 'image') {
      console.log('Opening lightbox...');
      setIsLightboxOpen(true);
    }
  };

  // Xử lý chuyển đến hình ảnh tiếp theo trong lightbox
  const handleNextImage = useCallback(() => {
    // Chỉ xử lý các hình ảnh, bỏ qua video
    const imageIndexes = galleryMedia
      .map((media, index) => (media.type === 'image' ? index : -1))
      .filter(index => index !== -1);

    if (imageIndexes.length <= 1) return; // Không có hình ảnh nào để chuyển đến

    const currentIndex = imageIndexes.indexOf(selectedMediaIndex);
    const nextIndex =
      currentIndex < imageIndexes.length - 1 ? imageIndexes[currentIndex + 1] : imageIndexes[0];

    if (nextIndex !== undefined) {
      setSelectedMediaIndex(nextIndex);
    }
  }, [galleryMedia, selectedMediaIndex]);

  // Xử lý chuyển đến hình ảnh trước đó trong lightbox
  const handlePrevImage = useCallback(() => {
    // Chỉ xử lý các hình ảnh, bỏ qua video
    const imageIndexes = galleryMedia
      .map((media, index) => (media.type === 'image' ? index : -1))
      .filter(index => index !== -1);

    if (imageIndexes.length <= 1) return; // Không có hình ảnh nào để chuyển đến

    const currentIndex = imageIndexes.indexOf(selectedMediaIndex);
    const prevIndex =
      currentIndex > 0 ? imageIndexes[currentIndex - 1] : imageIndexes[imageIndexes.length - 1];

    if (prevIndex !== undefined) {
      setSelectedMediaIndex(prevIndex);
    }
  }, [galleryMedia, selectedMediaIndex]);

  // Xử lý sự kiện phím
  React.useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (!isLightboxOpen) return;

      switch (e.key) {
        case 'ArrowLeft':
          handlePrevImage();
          break;
        case 'ArrowRight':
          handleNextImage();
          break;
        case 'Escape':
          setIsLightboxOpen(false);
          break;
        default:
          break;
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => {
      window.removeEventListener('keydown', handleKeyDown);
    };
  }, [isLightboxOpen, selectedMediaIndex, handlePrevImage, handleNextImage]);

  return (
    <>
      {/* Thêm CSS để ẩn thanh cuộn */}
      <style>{hideScrollbarCSS}</style>

      <div className={`${className} flex flex-col`}>
        {/* Media chính ở trên */}
        <div className=" rounded-lg overflow-hidden bg-gray-100 dark:bg-gray-800">
          {currentMedia.type === 'image' ? (
            <div className="relative group cursor-pointer" onClick={handleMainImageClick}>
              <img
                src={currentMedia.src}
                alt={currentMedia.alt}
                className="w-full h-auto object-contain aspect-video transition-transform duration-300 group-hover:scale-[1.02]"
              />
              <div className="absolute inset-0 flex items-center justify-center opacity-0 bg-black bg-opacity-20 transition-opacity duration-300 group-hover:opacity-100">
                <div className="bg-black bg-opacity-50 rounded-full p-3 transform transition-transform duration-300 group-hover:scale-110">
                  <Icon name="search" size="md" className="text-white" />
                </div>
              </div>
            </div>
          ) : (
            <video
              src={currentMedia.src}
              controls
              className="w-full h-auto object-contain aspect-video"
              poster={currentMedia.thumbnail}
            >
              Your browser does not support the video tag.
            </video>
          )}
        </div>

        {/* ImageGallery Lightbox */}
        {isLightboxOpen && (
          <div className="fixed inset-0 z-[9999] overflow-hidden lightbox-fade-in">
            <style>
              {`
              @keyframes lightboxFadeIn {
                from { opacity: 0; }
                to { opacity: 1; }
              }
              .lightbox-fade-in {
                animation: lightboxFadeIn 0.3s ease-in-out;
              }
            `}
            </style>
            <div
              className="w-screen h-screen bg-black bg-opacity-75 flex items-center justify-center"
              onClick={() => setIsLightboxOpen(false)}
            >
              <div
                className="w-full h-full flex items-center justify-center relative"
                onClick={e => e.stopPropagation()} // Ngăn sự kiện click lan ra ngoài
              >
                <div className="w-full h-full max-w-none flex items-center justify-center">
                  {/* Hiển thị hình ảnh đơn giản */}
                  <div className="w-full h-full flex items-center justify-center">
                    <img
                      src={currentMedia.src}
                      alt={currentMedia.alt}
                      className="max-w-full max-h-full object-contain"
                    />
                  </div>
                </div>

                {/* Hiển thị số lượng hình ảnh */}
                <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 bg-black bg-opacity-50 text-white px-4 py-2 rounded-full">
                  {galleryMedia
                    .filter(media => media.type === 'image')
                    .findIndex(media => media.src === currentMedia.src) + 1}{' '}
                  / {galleryMedia.filter(media => media.type === 'image').length}
                </div>

                {/* Nút điều hướng trái */}
                <button
                  className="absolute left-4 top-1/2 transform -translate-y-1/2 bg-black bg-opacity-50 hover:bg-opacity-70 text-white rounded-full p-3 transition-all"
                  onClick={handlePrevImage}
                  aria-label="Previous image"
                >
                  <Icon name="chevron-left" size="md" />
                </button>

                {/* Nút điều hướng phải */}
                <button
                  className="absolute right-4 top-1/2 transform -translate-y-1/2 bg-black bg-opacity-50 hover:bg-opacity-70 text-white rounded-full p-3 transition-all"
                  onClick={handleNextImage}
                  aria-label="Next image"
                >
                  <Icon name="chevron-right" size="md" />
                </button>
              </div>

              {/* Close button */}
              <button
                className="absolute top-4 right-4 text-white bg-black bg-opacity-30 hover:bg-opacity-50 rounded-full p-2 transition-all z-[10000]"
                onClick={() => setIsLightboxOpen(false)}
              >
                <Icon name="close" size="md" />
              </button>
            </div>
          </div>
        )}

        {/* Container cho danh sách thumbnails với nút điều hướng */}
        {/* Container cho thumbnails với margin-top để tạo khoảng cách */}
        <div className="relative  ">
          {/* Nút cuộn trái */}
          {galleryMedia.length > 5 && (
            <button
              className="absolute left-1 top-1/2 -translate-y-1/2 z-10 bg-white dark:bg-gray-800 rounded-full p-1.5 shadow-md hover:bg-gray-100 dark:hover:bg-gray-700"
              onClick={() => handleScroll('left')}
              aria-label="Scroll left"
            >
              <Icon name="chevron-left" className="w-5 h-5 text-gray-600 dark:text-gray-300" />
            </button>
          )}

          {/* Danh sách thumbnails có thể cuộn ngang */}
          <div
            ref={thumbnailsContainerRef}
            className="flex overflow-x-auto py-3 px-8 -mx-4 snap-x hide-scrollbar"
            style={{
              scrollbarWidth: 'none',
              msOverflowStyle: 'none',
              WebkitOverflowScrolling: 'touch',
            }}
          >
            {galleryMedia.map((media, index) => (
              <div
                key={`thumbnail-${index}`}
                className={`
                flex-shrink-0 w-1/6 min-w-[40px] max-w-80px] mx-1 snap-start
                cursor-pointer rounded-md overflow-hidden border transition-all
                ${
                  selectedMediaIndex === index
                    ? 'border-red-600 shadow-md'
                    : 'border-transparent hover:border-gray-300'
                }
              `}
                onClick={() => handleThumbnailClick(index)}
              >
                {media.type === 'image' ? (
                  <img
                    src={media.src}
                    alt={media.alt}
                    className="w-full h-auto object-cover aspect-square"
                  />
                ) : (
                  <div className="relative w-full aspect-square bg-gray-200 dark:bg-gray-700">
                    <img
                      src={media.thumbnail || media.src}
                      alt={media.alt}
                      className="w-full h-full object-cover"
                    />
                    <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-30">
                      <Icon name="image" className="w-8 h-8 text-white" />
                    </div>
                  </div>
                )}
              </div>
            ))}
          </div>

          {/* Nút cuộn phải */}
          {galleryMedia.length > 5 && (
            <button
              className="absolute right-1 top-1/2 -translate-y-1/2 z-10 bg-white dark:bg-gray-800 rounded-full p-1.5 shadow-md hover:bg-gray-100 dark:hover:bg-gray-700"
              onClick={() => handleScroll('right')}
              aria-label="Scroll right"
            >
              <Icon name="chevron-right" className="w-5 h-5 text-gray-600 dark:text-gray-300" />
            </button>
          )}
        </div>
      </div>
    </>
  );
};

export default ProductGallery;