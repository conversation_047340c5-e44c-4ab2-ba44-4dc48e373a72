import { Loading } from '@/shared/components';
import MainLayout from '@/shared/layouts/MainLayout';
import { Suspense, lazy } from 'react';
import { RouteObject } from 'react-router-dom';
import UserDatasetManagementPage from '../pages/UserDatasetManagementPage';
import i18n from '@/lib/i18n';
import { CreateDatasetGooglePage, CreateDatasetOpenAIPage } from '../pages';

// Import User Dataset module pages
const DataFineTunePage = lazy(() => import('../pages/DataFineTunePage'));

/**
 * User Dataset module routes
 */
const adminDatasetRoutes: RouteObject[] = [
  {
    path: '/admin-dataset',
    element: (
      <MainLayout title={i18n.t('admin:dataset.title', 'Quản lý Dataset & Model Admin')}>
        <Suspense fallback={<Loading />}>
          <UserDatasetManagementPage />
        </Suspense>
      </MainLayout>
    ),
  },
  {
    path: '/admin-dataset/data-fine-tune',
    element: (
      <MainLayout title={i18n.t('admin:dataset.dataFineTune.title', 'Dataset Fine-tune')}>
        <Suspense fallback={<Loading />}>
          <DataFineTunePage />
        </Suspense>
      </MainLayout>
    ),
  },

  {
    path: '/admin-dataset/create-openai',
    element: (
      <MainLayout title={i18n.t('admin:dataset.createDataset.openai.title', 'Tạo Dataset OpenAI')}>
        <Suspense fallback={<Loading />}>
          <CreateDatasetOpenAIPage />
        </Suspense>
      </MainLayout>
    ),
  },
  {
    path: '/admin-dataset/create-google',
    element: (
      <MainLayout title={i18n.t('admin:dataset.createDataset.google.title', 'Tạo Dataset Google')}>
        <Suspense fallback={<Loading />}>
          <CreateDatasetGooglePage />
        </Suspense>
      </MainLayout>
    ),
  },
];

export default adminDatasetRoutes;
