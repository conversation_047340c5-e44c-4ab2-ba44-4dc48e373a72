import { apiClient } from '@/shared/api';
import { UploadUrlResponse } from '../types/jsonl.types';

const API_BASE_URL = '/admin/data-fine-tune';

/**
 * Get upload URL for S3
 */
export const getUploadUrl = async (mime: string): Promise<UploadUrlResponse> => {
  const response = await apiClient.get<UploadUrlResponse>(
    `${API_BASE_URL}/upload-url?mime=${encodeURIComponent(mime)}`
  );
  return response.result;
};

/**
 * Upload file to S3 using the upload URL
 */
export const uploadFileToS3 = async (uploadUrl: string, file: File): Promise<void> => {
  const formData = new FormData();
  formData.append('file', file);

  const response = await fetch(uploadUrl, {
    method: 'PUT',
    body: file,
    headers: {
      'Content-Type': file.type,
    },
  });

  if (!response.ok) {
    throw new Error(`Upload failed: ${response.statusText}`);
  }
};

/**
 * Complete upload process: get upload URL, upload file, return view URL
 */
export const uploadImage = async (file: File): Promise<string> => {
  try {
    // Step 1: Get upload URL
    const { uploadUrl, viewUrl } = await getUploadUrl(file.type);
    
    // Step 2: Upload file to S3
    await uploadFileToS3(uploadUrl, file);
    
    // Step 3: Return view URL
    return viewUrl;
  } catch (error) {
    console.error('Error uploading image:', error);
    throw error;
  }
};
