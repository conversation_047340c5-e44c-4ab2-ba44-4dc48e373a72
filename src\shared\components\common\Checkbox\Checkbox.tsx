import React, { forwardRef, useMemo } from 'react';
import { useTheme } from '@/shared/contexts/theme';
import { useFormControl } from '@/shared/components/common/Form/hooks/useFormControl';
import {
  FormControlColor,
  FormControlSize,
  CheckboxVariant,
  getCheckboxVariantClasses,
} from '@/shared/components/common/Form/utils/formControlUtils';

export type { CheckboxVariant } from '@/shared/components/common/Form/utils/formControlUtils';
export type { FormControlColor as CheckboxColor } from '@/shared/components/common/Form/utils/formControlUtils';

export interface CheckboxProps {
  /**
   * Nhãn hiển thị bên cạnh checkbox
   */
  label?: string | React.ReactNode;

  /**
   * Trạng thái checked của checkbox
   */
  checked?: boolean;

  /**
   * Callback khi trạng thái checkbox thay đổi
   */
  onChange?: (checked: boolean) => void;

  /**
   * Gi<PERSON> trị của checkbox, thường dùng trong CheckboxGroup
   */
  value?: string | number | readonly string[];

  /**
   * Vô hiệu hóa checkbox
   */
  disabled?: boolean;

  /**
   * CSS classes tùy chỉnh
   */
  className?: string;

  /**
   * ID của checkbox
   */
  id?: string;

  /**
   * Tên của checkbox, dùng khi submit form
   */
  name?: string;

  /**
   * Trạng thái không xác định (một phần được chọn)
   */
  indeterminate?: boolean;

  /**
   * Kích thước của checkbox
   */
  size?: FormControlSize;

  /**
   * Biến thể của checkbox
   */
  variant?: CheckboxVariant;

  /**
   * Màu sắc của checkbox
   */
  color?: FormControlColor;

  /**
   * Callback khi blur, thường dùng với React Hook Form
   */
  onBlur?: () => void;
}

/**
 * Component Checkbox cho phép người dùng chọn một hoặc nhiều tùy chọn
 */
const Checkbox = forwardRef<HTMLInputElement, CheckboxProps>(
  (
    {
      label,
      checked = false,
      onChange,
      value,
      disabled = false,
      className = '',
      id,
      name,
      indeterminate = false,
      size = 'md',
      variant = 'filled',
      color = 'primary',
      onBlur,
      ...rest
    },
    ref
  ) => {
    useTheme(); // Sử dụng hook theme mới

    // Sử dụng custom hook để xử lý logic chung
    const { handleChange, sizeClasses, labelSizeClasses, colorClasses, ariaAttributes } =
      useFormControl({
        checked,
        onChange: onChange || (() => {}),
        disabled,
        size,
        color,
        indeterminate,
        onBlur,
      });

    // Memoize variant classes
    const variantClasses = useMemo(() => getCheckboxVariantClasses(variant), [variant]);

    return (
      <label
        className={`inline-flex items-center ${disabled ? 'opacity-60 cursor-not-allowed' : 'cursor-pointer'} ${className}`}
      >
        <div className="relative flex items-center justify-center">
          <input
            type="checkbox"
            ref={ref}
            checked={checked}
            onChange={handleChange}
            disabled={disabled}
            className={`
            appearance-none
            ${sizeClasses}
            border ${variant === 'outlined' ? 'border-2' : 'border'} ${checked || indeterminate ? colorClasses.border : 'border-border'}
            ${variantClasses}
            ${variant === 'filled' && checked ? colorClasses.filledBg : 'bg-card-muted'}
            ${variant === 'filled' && checked ? colorClasses.filledText : ''}
            focus:outline-none
            focus:ring-2 focus:ring-offset-2 ${colorClasses.ring}
            transition-all duration-200
            cursor-pointer
          `}
            id={id}
            name={name}
            value={value}
            {...ariaAttributes}
            {...rest}
          />

          {/* Custom checkbox icon */}
          {checked && (
            <svg
              className={`absolute pointer-events-none ${variant === 'filled' ? 'fill-primary-foreground' : colorClasses.fill} ${sizeClasses}`}
              viewBox="0 0 24 24"
              width="24"
              height="24"
              aria-hidden="true"
            >
              <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41L9 16.17z" />
            </svg>
          )}

          {/* Indeterminate state */}
          {!checked && indeterminate && (
            <div
              className={`
              absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2
              w-[60%] h-[2px] ${variant === 'filled' ? 'bg-primary-foreground' : colorClasses.dot} rounded-full
            `}
              aria-hidden="true"
            ></div>
          )}
        </div>

        {label && (
          <span
            className={`ml-2 ${labelSizeClasses} ${disabled ? 'text-muted' : 'text-foreground'} select-none`}
          >
            {label}
          </span>
        )}
      </label>
    );
  }
);

Checkbox.displayName = 'Checkbox';

export default Checkbox;
