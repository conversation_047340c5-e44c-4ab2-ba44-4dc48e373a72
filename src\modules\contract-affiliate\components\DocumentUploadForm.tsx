/**
 * Component upload tà<PERSON> li<PERSON>u cho hợp đồng affiliate
 */
import React, { useState, useRef } from 'react';
import { useTranslation } from 'react-i18next';
import { Button, Typography, Icon, Card } from '@/shared/components/common';
import { ContractAffiliateStepProps, ContractAffiliateType, DocumentUpload } from '../types';

const DocumentUploadForm: React.FC<ContractAffiliateStepProps> = ({ data, onNext, onPrevious, isLoading }) => {
  const { t } = useTranslation('contract-affiliate');
  const [documents, setDocuments] = useState<DocumentUpload>(data.documentUpload || {});
  const [errors, setErrors] = useState<Record<string, string>>({});
  
  const businessLicenseRef = useRef<HTMLInputElement>(null);
  const idCardFrontRef = useRef<HTMLInputElement>(null);
  const idCardBackRef = useRef<HTMLInputElement>(null);

  const isBusinessType = data.type === ContractAffiliateType.BUSINESS;

  const validateFile = (file: File): string | null => {
    const allowedTypes = ['application/pdf', 'image/jpeg', 'image/jpg', 'image/png'];
    const maxSize = 10 * 1024 * 1024; // 10MB

    if (!allowedTypes.includes(file.type)) {
      return t('contract-affiliate:validation.fileType');
    }

    if (file.size > maxSize) {
      return t('contract-affiliate:validation.fileSize');
    }

    return null;
  };

  const handleFileChange = (type: keyof DocumentUpload, file: File | null) => {
    if (!file) {
      setDocuments(prev => ({ ...prev, [type]: undefined }));
      setErrors(prev => ({ ...prev, [type]: '' }));
      return;
    }

    const error = validateFile(file);
    if (error) {
      setErrors(prev => ({ ...prev, [type]: error }));
      return;
    }

    setDocuments(prev => ({ ...prev, [type]: file }));
    setErrors(prev => ({ ...prev, [type]: '' }));
  };

  const handleNext = () => {
    const newErrors: Record<string, string> = {};

    if (isBusinessType) {
      if (!documents.businessLicense) {
        newErrors['businessLicense'] = t('contract-affiliate:validation.businessLicenseRequired');
      }
    } else {
      if (!documents.idCardFront) {
        newErrors['idCardFront'] = t('contract-affiliate:validation.idCardFrontRequired');
      }
      if (!documents.idCardBack) {
        newErrors['idCardBack'] = t('contract-affiliate:validation.idCardBackRequired');
      }
    }

    if (Object.keys(newErrors).length > 0) {
      setErrors(newErrors);
      return;
    }

    onNext({ documentUpload: documents });
  };

  const renderFileUpload = (
    type: keyof DocumentUpload,
    label: string,
    description: string,
    ref: React.RefObject<HTMLInputElement>,
    required = false
  ) => {
    const file = documents[type];
    const error = errors[type];

    return (
      <Card className="p-6">
        <div className="space-y-4">
          <div>
            <Typography variant="h4" className="font-medium">
              {label} {required && <span className="text-red-500">*</span>}
            </Typography>
            <Typography variant="body2" className="text-muted-foreground mt-1">
              {description}
            </Typography>
          </div>

          <div className="border-2 border-dashed border-border rounded-lg p-6 text-center">
            {file ? (
              <div className="space-y-3">
                <Icon name="file-check" className="w-12 h-12 mx-auto text-green-500" />
                <div>
                  <Typography variant="body2" className="font-medium">
                    {file.name}
                  </Typography>
                  <Typography variant="caption" className="text-muted-foreground">
                    {(file.size / 1024 / 1024).toFixed(2)} MB
                  </Typography>
                </div>
                <div className="flex gap-2 justify-center">
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={() => ref.current?.click()}
                  >
                    {t('contract-affiliate:documentUpload.changeFile')}
                  </Button>
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={() => handleFileChange(type, null)}
                  >
                    {t('contract-affiliate:documentUpload.removeFile')}
                  </Button>
                </div>
              </div>
            ) : (
              <div className="space-y-3">
                <Icon name="upload" className="w-12 h-12 mx-auto text-muted-foreground" />
                <div>
                  <Typography variant="body2" className="font-medium">
                    {t('contract-affiliate:documentUpload.clickToUpload')}
                  </Typography>
                  <Typography variant="caption" className="text-muted-foreground">
                    {t('contract-affiliate:documentUpload.supportedFormats')}
                  </Typography>
                  <Typography variant="caption" className="text-muted-foreground block">
                    {t('contract-affiliate:documentUpload.maxSize')}
                  </Typography>
                </div>
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => ref.current?.click()}
                >
                  {t('contract-affiliate:documentUpload.selectFile')}
                </Button>
              </div>
            )}
          </div>

          {error && (
            <Typography variant="caption" className="text-red-500">
              {error}
            </Typography>
          )}

          <input
            ref={ref}
            type="file"
            accept=".pdf,.jpg,.jpeg,.png"
            className="hidden"
            onChange={(e) => {
              const file = e.target.files?.[0] || null;
              handleFileChange(type, file);
              if (e.target) e.target.value = '';
            }}
          />
        </div>
      </Card>
    );
  };

  return (
    <div className="w-full space-y-6">
      <div>
        <Typography variant="h2" className="text-xl font-semibold mb-2">
          {t('contract-affiliate:documentUpload.title')}
        </Typography>
        <Typography variant="body1" className="text-muted-foreground">
          {isBusinessType 
            ? t('contract-affiliate:documentUpload.businessDescription')
            : t('contract-affiliate:documentUpload.personalDescription')
          }
        </Typography>
      </div>

      <div className="space-y-6">
        {isBusinessType ? (
          renderFileUpload(
            'businessLicense',
            t('contract-affiliate:documentUpload.businessLicense'),
            t('contract-affiliate:documentUpload.businessLicenseDescription'),
            businessLicenseRef,
            true
          )
        ) : (
          <>
            {renderFileUpload(
              'idCardFront',
              t('contract-affiliate:documentUpload.idCardFront'),
              t('contract-affiliate:documentUpload.idCardFrontDescription'),
              idCardFrontRef,
              true
            )}
            {renderFileUpload(
              'idCardBack',
              t('contract-affiliate:documentUpload.idCardBack'),
              t('contract-affiliate:documentUpload.idCardBackDescription'),
              idCardBackRef,
              true
            )}
          </>
        )}
      </div>

      {/* Navigation buttons */}
      <div className="flex justify-between mt-8">
        <Button
          type="button"
          variant="outline"
          onClick={onPrevious}
          disabled={isLoading}
        >
          {t('contract-affiliate:actions.previous')}
        </Button>

        <Button
          type="button"
          variant="primary"
          onClick={handleNext}
          isLoading={isLoading}
        >
          {t('contract-affiliate:actions.next')}
        </Button>
      </div>
    </div>
  );
};

export default DocumentUploadForm;
