/**
 * SMS Marketing Constants
 */

/**
 * SMS API Endpoints
 */
export const SMS_API_ENDPOINTS = {
  // Providers
  PROVIDERS: '/api/v1/marketing/sms/providers',
  PROVIDER_DETAIL: (id: string) => `/api/v1/marketing/sms/providers/${id}`,
  PROVIDER_TEST: (id: string) => `/api/v1/marketing/sms/providers/${id}/test`,
  PROVIDER_STATS: (id: string) => `/api/v1/marketing/sms/providers/${id}/stats`,
  
  // Campaigns
  CAMPAIGNS: '/api/v1/marketing/sms/campaigns',
  CAMPAIGN_DETAIL: (id: string) => `/api/v1/marketing/sms/campaigns/${id}`,
  CAMPAIGN_START: (id: string) => `/api/v1/marketing/sms/campaigns/${id}/start`,
  CAMPAIGN_PAUSE: (id: string) => `/api/v1/marketing/sms/campaigns/${id}/pause`,
  CAMPAIGN_STOP: (id: string) => `/api/v1/marketing/sms/campaigns/${id}/stop`,
  CAMPAIGN_DUPLICATE: (id: string) => `/api/v1/marketing/sms/campaigns/${id}/duplicate`,
  CAMPAIGN_ANALYTICS: (id: string) => `/api/v1/marketing/sms/campaigns/${id}/analytics`,
  CAMPAIGN_RECIPIENTS: (id: string) => `/api/v1/marketing/sms/campaigns/${id}/recipients`,
  
  // Templates
  TEMPLATES: '/api/v1/marketing/sms/templates',
  TEMPLATE_DETAIL: (id: string) => `/api/v1/marketing/sms/templates/${id}`,
  TEMPLATE_PREVIEW: (id: string) => `/api/v1/marketing/sms/templates/${id}/preview`,
  TEMPLATE_VALIDATE: (id: string) => `/api/v1/marketing/sms/templates/${id}/validate`,
  TEMPLATE_DUPLICATE: (id: string) => `/api/v1/marketing/sms/templates/${id}/duplicate`,
  TEMPLATE_ANALYTICS: (id: string) => `/api/v1/marketing/sms/templates/${id}/analytics`,
  
  // Contacts
  CONTACTS: '/api/v1/marketing/sms/contacts',
  CONTACT_DETAIL: (id: string) => `/api/v1/marketing/sms/contacts/${id}`,
  CONTACT_LISTS: '/api/v1/marketing/sms/contact-lists',
  CONTACT_LIST_DETAIL: (id: string) => `/api/v1/marketing/sms/contact-lists/${id}`,
  CONTACT_IMPORT: '/api/v1/marketing/sms/contacts/import',
  CONTACT_EXPORT: '/api/v1/marketing/sms/contacts/export',
  
  // Analytics
  ANALYTICS_OVERVIEW: '/api/v1/marketing/sms/analytics/overview',
  ANALYTICS_CAMPAIGNS: '/api/v1/marketing/sms/analytics/campaigns',
  ANALYTICS_PROVIDERS: '/api/v1/marketing/sms/analytics/providers',
  ANALYTICS_GEOGRAPHIC: '/api/v1/marketing/sms/analytics/geographic',
  
  // Messages
  MESSAGES: '/api/v1/marketing/sms/messages',
  MESSAGE_DETAIL: (id: string) => `/api/v1/marketing/sms/messages/${id}`,
  SEND_MESSAGE: '/api/v1/marketing/sms/messages/send',
  
  // Settings
  SETTINGS: '/api/v1/marketing/sms/settings',
  OPT_OUTS: '/api/v1/marketing/sms/opt-outs',
  WEBHOOKS: '/api/v1/marketing/sms/webhooks',
} as const;

/**
 * SMS Provider Types
 */
export const SMS_PROVIDER_TYPES = {
  TWILIO: 'twilio',
  AWS_SNS: 'aws-sns',
  VIETTEL: 'viettel',
  VNPT: 'vnpt',
  FPT: 'fpt',
  CUSTOM: 'custom',
} as const;



/**
 * SMS Provider Labels
 */
export const SMS_PROVIDER_LABELS = {
  [SMS_PROVIDER_TYPES.TWILIO]: 'Twilio',
  [SMS_PROVIDER_TYPES.AWS_SNS]: 'AWS SNS',
  [SMS_PROVIDER_TYPES.VIETTEL]: 'Viettel SMS',
  [SMS_PROVIDER_TYPES.VNPT]: 'VNPT SMS',
  [SMS_PROVIDER_TYPES.FPT]: 'FPT SMS',
  [SMS_PROVIDER_TYPES.CUSTOM]: 'Custom API',
} as const;

/**
 * SMS Campaign Status
 */
export const SMS_CAMPAIGN_STATUS = {
  DRAFT: 'draft',
  SCHEDULED: 'scheduled',
  SENDING: 'sending',
  SENT: 'sent',
  PAUSED: 'paused',
  CANCELLED: 'cancelled',
  FAILED: 'failed',
} as const;

/**
 * SMS Campaign Status Labels
 */
export const SMS_CAMPAIGN_STATUS_LABELS = {
  [SMS_CAMPAIGN_STATUS.DRAFT]: 'Bản nháp',
  [SMS_CAMPAIGN_STATUS.SCHEDULED]: 'Đã lên lịch',
  [SMS_CAMPAIGN_STATUS.SENDING]: 'Đang gửi',
  [SMS_CAMPAIGN_STATUS.SENT]: 'Đã gửi',
  [SMS_CAMPAIGN_STATUS.PAUSED]: 'Tạm dừng',
  [SMS_CAMPAIGN_STATUS.CANCELLED]: 'Đã hủy',
  [SMS_CAMPAIGN_STATUS.FAILED]: 'Thất bại',
} as const;

/**
 * SMS Campaign Status Colors
 */
export const SMS_CAMPAIGN_STATUS_COLORS = {
  [SMS_CAMPAIGN_STATUS.DRAFT]: 'gray',
  [SMS_CAMPAIGN_STATUS.SCHEDULED]: 'blue',
  [SMS_CAMPAIGN_STATUS.SENDING]: 'orange',
  [SMS_CAMPAIGN_STATUS.SENT]: 'green',
  [SMS_CAMPAIGN_STATUS.PAUSED]: 'yellow',
  [SMS_CAMPAIGN_STATUS.CANCELLED]: 'red',
  [SMS_CAMPAIGN_STATUS.FAILED]: 'red',
} as const;

/**
 * SMS Template Categories
 */
export const SMS_TEMPLATE_CATEGORIES = {
  MARKETING: 'marketing',
  TRANSACTIONAL: 'transactional',
  REMINDER: 'reminder',
  ALERT: 'alert',
  OTP: 'otp',
  NOTIFICATION: 'notification',
  WELCOME: 'welcome',
  PROMOTIONAL: 'promotional',
} as const;

/**
 * SMS Template Category Labels
 */
export const SMS_TEMPLATE_CATEGORY_LABELS = {
  [SMS_TEMPLATE_CATEGORIES.MARKETING]: 'Marketing',
  [SMS_TEMPLATE_CATEGORIES.TRANSACTIONAL]: 'Giao dịch',
  [SMS_TEMPLATE_CATEGORIES.REMINDER]: 'Nhắc nhở',
  [SMS_TEMPLATE_CATEGORIES.ALERT]: 'Cảnh báo',
  [SMS_TEMPLATE_CATEGORIES.OTP]: 'OTP',
  [SMS_TEMPLATE_CATEGORIES.NOTIFICATION]: 'Thông báo',
  [SMS_TEMPLATE_CATEGORIES.WELCOME]: 'Chào mừng',
  [SMS_TEMPLATE_CATEGORIES.PROMOTIONAL]: 'Khuyến mãi',
} as const;

/**
 * SMS Template Status
 */
export const SMS_TEMPLATE_STATUS = {
  DRAFT: 'draft',
  ACTIVE: 'active',
  INACTIVE: 'inactive',
  ARCHIVED: 'archived',
  PENDING_APPROVAL: 'pending_approval',
  REJECTED: 'rejected',
} as const;

/**
 * SMS Template Status Labels
 */
export const SMS_TEMPLATE_STATUS_LABELS = {
  [SMS_TEMPLATE_STATUS.DRAFT]: 'Bản nháp',
  [SMS_TEMPLATE_STATUS.ACTIVE]: 'Hoạt động',
  [SMS_TEMPLATE_STATUS.INACTIVE]: 'Không hoạt động',
  [SMS_TEMPLATE_STATUS.ARCHIVED]: 'Đã lưu trữ',
  [SMS_TEMPLATE_STATUS.PENDING_APPROVAL]: 'Chờ duyệt',
  [SMS_TEMPLATE_STATUS.REJECTED]: 'Bị từ chối',
} as const;

/**
 * SMS Template Status Colors
 */
export const SMS_TEMPLATE_STATUS_COLORS = {
  [SMS_TEMPLATE_STATUS.DRAFT]: 'gray',
  [SMS_TEMPLATE_STATUS.ACTIVE]: 'green',
  [SMS_TEMPLATE_STATUS.INACTIVE]: 'yellow',
  [SMS_TEMPLATE_STATUS.ARCHIVED]: 'gray',
  [SMS_TEMPLATE_STATUS.PENDING_APPROVAL]: 'blue',
  [SMS_TEMPLATE_STATUS.REJECTED]: 'red',
} as const;

/**
 * SMS Delivery Status
 */
export const SMS_DELIVERY_STATUS = {
  PENDING: 'pending',
  SENT: 'sent',
  DELIVERED: 'delivered',
  FAILED: 'failed',
  BOUNCED: 'bounced',
  UNSUBSCRIBED: 'unsubscribed',
} as const;

/**
 * SMS Delivery Status Labels
 */
export const SMS_DELIVERY_STATUS_LABELS = {
  [SMS_DELIVERY_STATUS.PENDING]: 'Chờ gửi',
  [SMS_DELIVERY_STATUS.SENT]: 'Đã gửi',
  [SMS_DELIVERY_STATUS.DELIVERED]: 'Đã nhận',
  [SMS_DELIVERY_STATUS.FAILED]: 'Thất bại',
  [SMS_DELIVERY_STATUS.BOUNCED]: 'Bị trả về',
  [SMS_DELIVERY_STATUS.UNSUBSCRIBED]: 'Đã hủy đăng ký',
} as const;

/**
 * SMS Delivery Status Colors
 */
export const SMS_DELIVERY_STATUS_COLORS = {
  [SMS_DELIVERY_STATUS.PENDING]: 'blue',
  [SMS_DELIVERY_STATUS.SENT]: 'orange',
  [SMS_DELIVERY_STATUS.DELIVERED]: 'green',
  [SMS_DELIVERY_STATUS.FAILED]: 'red',
  [SMS_DELIVERY_STATUS.BOUNCED]: 'red',
  [SMS_DELIVERY_STATUS.UNSUBSCRIBED]: 'gray',
} as const;

/**
 * SMS Default Values
 */
export const SMS_DEFAULTS = {
  // Pagination
  PAGE_SIZE: 20,
  DEFAULT_PAGE: 1,
  
  // SMS Limits
  MAX_SMS_LENGTH: 160,
  MAX_UNICODE_SMS_LENGTH: 70,
  MAX_CONCATENATED_SMS: 6,
  
  // Rate Limits
  DEFAULT_RATE_LIMIT_PER_SECOND: 10,
  DEFAULT_RATE_LIMIT_PER_MINUTE: 100,
  DEFAULT_RATE_LIMIT_PER_HOUR: 1000,
  
  // Retry Settings
  MAX_RETRY_ATTEMPTS: 3,
  RETRY_DELAY_MINUTES: 5,
  
  // Cost Settings
  DEFAULT_COST_PER_SMS: 0.05, // USD
  
  // Template Settings
  MAX_TEMPLATE_NAME_LENGTH: 100,
  MAX_TEMPLATE_DESCRIPTION_LENGTH: 500,
  MAX_VARIABLES_PER_TEMPLATE: 20,
  
  // Campaign Settings
  MAX_CAMPAIGN_NAME_LENGTH: 100,
  MAX_CAMPAIGN_DESCRIPTION_LENGTH: 1000,
  MAX_RECIPIENTS_PER_CAMPAIGN: 100000,
  
  // Contact Settings
  MAX_CONTACT_LISTS: 50,
  MAX_CONTACTS_PER_LIST: 50000,
  
  // Date Ranges
  DEFAULT_ANALYTICS_DAYS: 30,
  MAX_ANALYTICS_DAYS: 365,
} as const;

/**
 * SMS Query Keys for TanStack Query
 */
const SMS_BASE_KEY = ['sms'] as const;

export const SMS_QUERY_KEYS = {
  ALL: SMS_BASE_KEY,

  // Providers
  PROVIDERS: () => [...SMS_BASE_KEY, 'providers'] as const,
  PROVIDER: (id: string) => [...SMS_BASE_KEY, 'providers', id] as const,
  PROVIDER_STATS: (id: string) => [...SMS_BASE_KEY, 'providers', id, 'stats'] as const,

  // Campaigns
  CAMPAIGNS: (params?: unknown) => [...SMS_BASE_KEY, 'campaigns', params] as const,
  CAMPAIGN: (id: string) => [...SMS_BASE_KEY, 'campaigns', id] as const,
  CAMPAIGN_ANALYTICS: (id: string) => [...SMS_BASE_KEY, 'campaigns', id, 'analytics'] as const,
  CAMPAIGN_RECIPIENTS: (id: string) => [...SMS_BASE_KEY, 'campaigns', id, 'recipients'] as const,

  // Templates
  TEMPLATES: (params?: unknown) => [...SMS_BASE_KEY, 'templates', params] as const,
  TEMPLATE: (id: string) => [...SMS_BASE_KEY, 'templates', id] as const,
  TEMPLATE_ANALYTICS: (id: string) => [...SMS_BASE_KEY, 'templates', id, 'analytics'] as const,
  TEMPLATE_CATEGORIES: [...SMS_BASE_KEY, 'template-categories'] as const,

  // Contacts
  CONTACTS: (params?: unknown) => [...SMS_BASE_KEY, 'contacts', params] as const,
  CONTACT: (id: string) => [...SMS_BASE_KEY, 'contacts', id] as const,
  CONTACT_LISTS: (params?: unknown) => [...SMS_BASE_KEY, 'contact-lists', params] as const,
  CONTACT_LIST: (id: string) => [...SMS_BASE_KEY, 'contact-lists', id] as const,

  // Analytics
  ANALYTICS: (params?: unknown) => [...SMS_BASE_KEY, 'analytics', params] as const,
  ANALYTICS_OVERVIEW: () => [...SMS_BASE_KEY, 'analytics', 'overview'] as const,
  ANALYTICS_CAMPAIGNS: () => [...SMS_BASE_KEY, 'analytics', 'campaigns'] as const,
  ANALYTICS_PROVIDERS: () => [...SMS_BASE_KEY, 'analytics', 'providers'] as const,

  // Messages
  MESSAGES: (params?: unknown) => [...SMS_BASE_KEY, 'messages', params] as const,
  MESSAGE: (id: string) => [...SMS_BASE_KEY, 'messages', id] as const,

  // Settings
  SETTINGS: () => [...SMS_BASE_KEY, 'settings'] as const,
  OPT_OUTS: (params?: unknown) => [...SMS_BASE_KEY, 'opt-outs', params] as const,
  WEBHOOKS: (params?: unknown) => [...SMS_BASE_KEY, 'webhooks', params] as const,
} as const;

/**
 * SMS Validation Rules
 */
export const SMS_VALIDATION = {
  PHONE_NUMBER_REGEX: /^\+?[1-9]\d{1,14}$/,
  VARIABLE_REGEX: /\{\{([a-zA-Z_][a-zA-Z0-9_]*)\}\}/g,
  SENDER_ID_REGEX: /^[a-zA-Z0-9\s]{1,11}$/,
  SENDER_ID_NUMERIC_REGEX: /^\+?[1-9]\d{1,15}$/,
} as const;

/**
 * SMS Error Codes
 */
export const SMS_ERROR_CODES = {
  INVALID_PHONE_NUMBER: 'INVALID_PHONE_NUMBER',
  INVALID_MESSAGE_CONTENT: 'INVALID_MESSAGE_CONTENT',
  PROVIDER_NOT_AVAILABLE: 'PROVIDER_NOT_AVAILABLE',
  RATE_LIMIT_EXCEEDED: 'RATE_LIMIT_EXCEEDED',
  INSUFFICIENT_BALANCE: 'INSUFFICIENT_BALANCE',
  TEMPLATE_NOT_FOUND: 'TEMPLATE_NOT_FOUND',
  CAMPAIGN_NOT_FOUND: 'CAMPAIGN_NOT_FOUND',
  CONTACT_OPTED_OUT: 'CONTACT_OPTED_OUT',
  MESSAGE_TOO_LONG: 'MESSAGE_TOO_LONG',
  INVALID_SENDER_ID: 'INVALID_SENDER_ID',
} as const;
