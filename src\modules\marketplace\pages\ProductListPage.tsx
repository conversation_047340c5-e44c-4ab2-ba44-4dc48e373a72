import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { Link, useParams, useNavigate } from 'react-router-dom';
import {
  Loading,
  Pagination,
  Button,
  Icon,
  Chip,
  Typography,
  ResponsiveGrid,
} from '@/shared/components/common';
import Alert from '@/shared/components/common/Alert';
import { useTheme } from '@/shared/contexts';
import { useProductList } from '../hooks/useProductList';
import { ProductListItem } from '../types/product.types';
import ProductCard from '../components/ProductCard';
import MenuIconBar from '@/modules/components/menu-bar/MenuIconBar';

/**
 * Trang hiển thị danh sách sản phẩm
 */
const ProductListPage: React.FC = () => {
  const { t } = useTranslation(['marketplace', 'common']);
  const { category } = useParams<{ category?: string }>();
  const navigate = useNavigate();
  const [page, setPage] = useState(1);
  const [limit, setLimit] = useState(10);
  const [search, setSearch] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string | undefined>(category);
  const [priceRange, setPriceRange] = useState<[number, number]>([0, 10000000]);
  const [sortBy, setSortBy] = useState<'price' | 'rating' | 'newest'>('newest');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');

  // Sử dụng hook theme
  useTheme();

  // Lấy dữ liệu sản phẩm từ hook
  const {
    data,
    isLoading,
    error,
    categories,
    priceRange: maxPriceRange,
    // refetch, // Unused for now
  } = useProductList({
    page,
    limit,
    search,
    ...(selectedCategory && { category: selectedCategory }),
    minPrice: priceRange[0],
    maxPrice: priceRange[1],
    sortBy,
    sortOrder,
  });

  // Xử lý khi thay đổi trang
  const handlePageChange = (newPage: number) => {
    setPage(newPage);
    window.scrollTo(0, 0);
  };

  // Xử lý khi thay đổi số lượng sản phẩm trên trang
  const handleLimitChange = (newLimit: number) => {
    setLimit(newLimit);
    setPage(1); // Reset về trang đầu khi thay đổi limit
  };

  // Xử lý khi tìm kiếm - nhận tham số từ MenuIconBar
  const handleSearch = (searchTerm: string) => {
    setSearch(searchTerm);
    setPage(1);
  };

  // Xử lý khi thay đổi bộ lọc
  const handleFilterChange = (filter: string) => {
    if (filter === 'all') {
      setSelectedCategory(undefined);
      setPriceRange([maxPriceRange?.min || 0, maxPriceRange?.max || 10000000]);
    } else {
      // Sử dụng trực tiếp enum value từ API
      setSelectedCategory(filter);
      setPriceRange([maxPriceRange?.min || 0, maxPriceRange?.max || 10000000]);
    }

    setPage(1);
  };

  // Reset page when category changes
  useEffect(() => {
    setSelectedCategory(category);
    setPage(1);
  }, [category]);

  // Update price range when maxPriceRange changes
  useEffect(() => {
    if (maxPriceRange) {
      setPriceRange([maxPriceRange.min, maxPriceRange.max]);
    }
  }, [maxPriceRange]);

  // Get category name from slug
  const getCategoryName = (categorySlug?: string): string => {
    if (!categorySlug) return '';

    const category = categories.find(cat => cat.slug === categorySlug);
    if (category) return category.name;

    // Convert slug to readable name if category not found
    return categorySlug
      .split('-')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
  };

  return (
    <div>
      <MenuIconBar
        onSearch={handleSearch}
        items={[
          {
            id: 'all',
            label: t('common:all', 'Tất cả'),
            icon: 'list',
            onClick: () => handleFilterChange('all'),
          },
          {
            id: 'AGENT',
            label: t('marketplace:categories.agent', 'AI Agent'),
            icon: 'robot',
            onClick: () => handleFilterChange('AGENT'),
          },
          {
            id: 'KNOWLEDGE_FILE',
            label: t('marketplace:categories.knowledgeFile', 'Knowledge File'),
            icon: 'document',
            onClick: () => handleFilterChange('KNOWLEDGE_FILE'),
          },
          {
            id: 'FUNCTION',
            label: t('marketplace:categories.function', 'Function'),
            icon: 'code',
            onClick: () => handleFilterChange('FUNCTION'),
          },
          {
            id: 'FINETUNE',
            label: t('marketplace:categories.finetune', 'Fine-tuned Model'),
            icon: 'brain',
            onClick: () => handleFilterChange('FINETUNE'),
          },
          {
            id: 'STRATEGY',
            label: t('marketplace:categories.strategy', 'Strategy'),
            icon: 'chart',
            onClick: () => handleFilterChange('STRATEGY'),
          },
        ]}
        additionalIcons={[
          {
            icon: 'marketplace',
            tooltip: t('marketplace:myProducts', 'Sản phẩm đăng bán'),
            variant: 'primary',
            onClick: () => navigate('/marketplace/products-for-sale'),
          },
        ]}
      />

      {/* Active category */}
      {category && (
        <div className="mb-6 flex items-center">
          <Typography variant="body1" className="mr-2">
            {t('marketplace.activeCategory', 'Danh mục:')}
          </Typography>
          <Chip variant="primary" size="md" className="flex items-center gap-1">
            {getCategoryName(category)}
            <Button
              variant="ghost"
              className="p-0 ml-1 text-white"
              onClick={() => navigate('/marketplace')}
            >
              <Icon name="close" size="sm" />
            </Button>
          </Chip>
        </div>
      )}

      {/* Product list */}
      {isLoading && (
        <div className="flex justify-center items-center min-h-[400px]">
          <Loading />
        </div>
      )}

      {error && (
        <Alert
          type="error"
          title={t('marketplace:errorTitle', 'Đã xảy ra lỗi')}
          message={error instanceof Error ? error.message : 'Unknown error'}
          className="my-4"
        />
      )}

      {!isLoading && !error && data && data.data.length > 0 && (
        <>
          <ResponsiveGrid
            maxColumns={{ xs: 1, sm: 2, md: 2, lg: 3, xl: 3 }}
            maxColumnsWithChatPanel={{ xs: 1, sm: 1, md: 2, lg: 2, xl: 2 }}
          >
            {data.data.map((product: ProductListItem) => (
              <Link
                key={product.id}
                to={`/marketplace/product/${product.id}`}
                className="block h-full"
              >
                <ProductCard
                  product={product}
                  onCategoryClick={(categorySlug, e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    navigate(`/marketplace/category/${categorySlug}`);
                  }}
                />
              </Link>
            ))}
          </ResponsiveGrid>

          {/* Pagination - With items per page selector */}
          {data.total > 0 && (
            <div className="flex justify-end mt-6">
              <Pagination
                variant="compact"
                currentPage={page}
                totalPages={Math.ceil(data.total / limit)}
                onPageChange={handlePageChange}
                showFirstLastButtons={false}
                showItemsPerPageSelector={true}
                itemsPerPage={limit}
                onItemsPerPageChange={handleLimitChange}
                itemsPerPageOptions={[10, 20, 50, 100]}
                showPageInfo={false}
                maxPageButtons={5}
                size="md"
                borderless={false}
              />
            </div>
          )}
        </>
      )}

      {/* Empty state */}
      {!isLoading && !error && data && data.data.length === 0 && (
        <div className="text-center py-12">
          <Icon name="document" size="xl" className="mx-auto mb-4 text-gray-400" />
          <Typography variant="body1" color="muted" className="text-lg">
            {t('marketplace:noProducts', 'Không tìm thấy sản phẩm nào.')}
          </Typography>
          <Button
            variant="primary"
            className="mt-4"
            onClick={() => {
              setSearch('');
              setSelectedCategory(undefined);
              setPriceRange([maxPriceRange?.min || 0, maxPriceRange?.max || 10000000]);
              setSortBy('newest');
              setSortOrder('desc');
            }}
          >
            {t('marketplace:products.resetFilters', 'Xóa bộ lọc')}
          </Button>
        </div>
      )}
    </div>
  );
};

export default ProductListPage;
