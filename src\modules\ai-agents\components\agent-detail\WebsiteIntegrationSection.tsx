import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import {
  CollapsibleCard,
  Button,
  Icon,
  Card,
  EmptyState,
  Typography,
  Tooltip,
} from '@/shared/components/common';
import { Website } from '../../types/agent.types';
import IntegrationSlideInForm from './IntegrationSlideInForm';
import { useWebsites, useConnectWebsites, useDisconnectWebsite } from '../../hooks/useAgentIntegrations';

interface WebsiteIntegrationSectionProps {
  agentId: string;
  websites: Website[];
  onToggle?: (isOpen: boolean) => void;
}

/**
 * Component hiển thị tích hợp Websites
 */
const WebsiteIntegrationSection: React.FC<WebsiteIntegrationSectionProps> = ({
  agentId,
  websites,
  onToggle,
}) => {
  const { t } = useTranslation();
  const [isFormVisible, setIsFormVisible] = useState(false);
  const [search, setSearch] = useState('');

  // Sử dụng hooks
  const { data: availableWebsites, isLoading } = useWebsites(search);
  const { mutate: connectWebsites, isPending: isConnecting } = useConnectWebsites(agentId);
  const { mutate: disconnectWebsite, isPending: isDisconnecting } = useDisconnectWebsite(agentId);

  // Xử lý mở form
  const handleOpenForm = () => {
    setIsFormVisible(true);
  };

  // Xử lý đóng form
  const handleCloseForm = () => {
    setIsFormVisible(false);
    setSearch('');
  };

  // Xử lý kết nối websites
  const handleConnectWebsites = (selectedIds: string[]) => {
    if (!selectedIds.length) return;

    connectWebsites(selectedIds, {
      onSuccess: () => {
        handleCloseForm();
        // Hiển thị thông báo thành công
        alert(t('aiAgents.website.connectSuccess', 'Kết nối Websites thành công!'));
      },
      onError: (error) => {
        // Hiển thị thông báo lỗi
        console.error('Connect Websites error:', error);
        alert(t('aiAgents.website.connectError', 'Có lỗi xảy ra khi kết nối Websites. Vui lòng thử lại.'));
      }
    });
  };

  // Xử lý ngắt kết nối website
  const handleDisconnectWebsite = (websiteId: string) => {
    // Hiển thị xác nhận trước khi ngắt kết nối
    if (window.confirm(t('aiAgents.website.confirmDisconnect', 'Bạn có chắc chắn muốn ngắt kết nối Website này?'))) {
      disconnectWebsite(websiteId, {
        onSuccess: () => {
          // Hiển thị thông báo thành công
          alert(t('aiAgents.website.disconnectSuccess', 'Ngắt kết nối Website thành công!'));
        },
        onError: (error) => {
          // Hiển thị thông báo lỗi
          console.error('Disconnect Website error:', error);
          alert(t('aiAgents.website.disconnectError', 'Có lỗi xảy ra khi ngắt kết nối Website. Vui lòng thử lại.'));
        }
      });
    }
  };

  // Xử lý khi đóng/mở card
  const handleCardToggle = (isOpen: boolean) => {
    if (onToggle) {
      onToggle(isOpen);
    }
  };

  return (
    <>
      <CollapsibleCard
        title={
          <div className="flex items-center">
            <Icon name="globe" className="mr-2 text-blue-500" />
            <span>{t('aiAgents.website.title', 'Tích hợp Website')}</span>
          </div>
        }
        className="mb-6"
        onToggle={handleCardToggle}
      >
        <div className="mb-4">
          <Typography variant="body1">
            {t(
              'aiAgents.website.description',
              'Kết nối Agent với các website để tương tác với khách hàng trên website của bạn.'
            )}
          </Typography>
        </div>

        {/* Danh sách Websites đã kết nối */}
        {websites.length === 0 ? (
          <EmptyState
            icon="globe"
            title={t('aiAgents.website.noWebsites', 'Chưa có Website nào được kết nối')}
            description={t(
              'aiAgents.website.addWebsiteDescription',
              'Kết nối Websites để Agent có thể tương tác với khách hàng trên website của bạn.'
            )}
            actions={
              <Button
                variant="primary"
                leftIcon={<Icon name="plus" size="sm" />}
                onClick={handleOpenForm}
              >
                {t('aiAgents.website.addWebsite', 'Thêm Website')}
              </Button>
            }
          />
        ) : (
          <>
            <div className="flex justify-between items-center mb-4">
              <Typography variant="subtitle1">
                {t('aiAgents.website.connectedWebsites', 'Websites đã kết nối')}
              </Typography>
              <Button
                variant="outline"
                size="sm"
                leftIcon={<Icon name="plus" size="sm" />}
                onClick={handleOpenForm}
              >
                {t('aiAgents.website.addWebsite', 'Thêm Website')}
              </Button>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {websites.map(website => (
                <Card key={website.id} className="p-4">
                  <div className="flex items-center">
                    {website.favicon && (
                      <img
                        src={website.favicon}
                        alt={website.name}
                        className="w-8 h-8 mr-3 object-contain"
                      />
                    )}
                    <div className="flex-1 min-w-0">
                      <Typography variant="subtitle1" className="truncate">
                        {website.name}
                      </Typography>
                      <Typography variant="caption" className="text-gray-500 truncate">
                        {website.url}
                      </Typography>
                    </div>
                    <Tooltip content={t('common.disconnect', 'Ngắt kết nối')} position="top">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleDisconnectWebsite(website.id)}
                        isLoading={isDisconnecting}
                      >
                        <Icon name="x-circle" className="text-red-500" />
                      </Button>
                    </Tooltip>
                  </div>
                </Card>
              ))}
            </div>
          </>
        )}
      </CollapsibleCard>

      {/* Form trượt để chọn Websites */}
      <IntegrationSlideInForm
        isVisible={isFormVisible}
        onClose={handleCloseForm}
        title={t('aiAgents.website.selectWebsites', 'Chọn Websites')}
        items={
          availableWebsites?.map(website => ({
            ...website,
            imageUrl: website.favicon || '',
          })) || []
        }
        selectedItems={websites.map(website => website.id)}
        onSelect={handleConnectWebsites}
        isLoading={isLoading}
        isSubmitting={isConnecting}
        onSearch={setSearch}
        searchPlaceholder={t('aiAgents.website.searchPlaceholder', 'Tìm kiếm Websites...')}
        emptyStateMessage={t(
          'aiAgents.website.noResults',
          'Không tìm thấy Websites. Vui lòng thử lại với từ khóa khác.'
        )}
      />
    </>
  );
};

export default WebsiteIntegrationSection;
