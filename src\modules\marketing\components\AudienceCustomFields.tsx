import React, { useState, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { Typography, CollapsibleCard, IconCard, GenericCustomFieldSelector } from '@/shared/components/common';
import CustomFieldRenderer from '@/modules/business/components/CustomFieldRenderer';
import { MarketingCustomFieldBusinessService } from '../services/marketing-custom-field.service';
import { useMarketingCustomFields } from '../hooks/useMarketingCustomFieldQuery';
import { Audience } from '../types/audience.types';
import { MarketingCustomFieldResponse } from '../types/custom-field.types';

// Interface cho trường tùy chỉnh đã chọn với giá trị - mở rộng từ MarketingCustomFieldResponse
interface SelectedCustomField extends MarketingCustomFieldResponse {
  value: Record<string, unknown>; // Giá trị người dùng nhập
}

interface AudienceCustomFieldsProps {
  audience: Audience;
}

/**
 * Component hiển thị trường tùy chỉnh của audience
 */
const AudienceCustomFields: React.FC<AudienceCustomFieldsProps> = ({ audience }) => {
  const { t } = useTranslation(['marketing', 'common']);

  // State
  const [audienceCustomFields, setAudienceCustomFields] = useState<SelectedCustomField[]>([]);
  const [hasChanges, setHasChanges] = useState(false);
  const [isSaving, setIsSaving] = useState(false);

  // Create search function for GenericCustomFieldSelector
  const searchFunction = useCallback(async (params: { search?: string; page?: number; limit?: number }) => {
    console.log('🔍 AudienceCustomFields searchFunction called with params:', params);

    try {
      const apiParams = {
        search: params.search,
        page: params.page || 1,
        limit: params.limit || 20,
        sortBy: 'id',
        sortDirection: 'ASC' as const,
        // Thêm timestamp để tránh cache
        _t: Date.now(),
      };

      console.log('📡 Calling MarketingCustomFieldBusinessService with:', apiParams);
      const response = await MarketingCustomFieldBusinessService.getCustomFieldsWithBusinessLogic(apiParams);
      console.log('✅ API response:', response);

      // Transform data for GenericCustomFieldSelector
      const items = response.result?.items || [];
      const meta = response.result?.meta || {};

      const transformedResult = {
        items: items.map((item: any) => ({
          id: item.id,
          label: item.displayName,
          dataType: item.dataType,
          type: item.dataType,
        })),
        totalItems: meta.totalItems || 0,
        totalPages: meta.totalPages || 0,
        currentPage: meta.currentPage || 1,
        hasNextPage: (meta.currentPage || 1) < (meta.totalPages || 1),
      };

      console.log('🔄 Transformed result:', transformedResult);
      return transformedResult;
    } catch (error) {
      console.error('❌ Error in marketing custom field search:', error);
      throw error;
    }
  }, []);

  // Fetch marketing custom fields để có thông tin đầy đủ
  const { data: customFieldsData } = useMarketingCustomFields({});
  const customFields = customFieldsData?.items || [];

  // Handle add custom field - cấu trúc mới
  const handleAddCustomField = useCallback((fieldData: {
    id: number;
    label: string;
    dataType: string;
    type: string;
  }) => {
    setAudienceCustomFields(prev => {
      // Check if field already exists
      if (prev.some(f => f.id === fieldData.id)) {
        return prev;
      }

      // Tìm custom field từ danh sách để lấy thông tin đầy đủ
      const customField = customFields.find(field => field.id === fieldData.id);
      if (!customField) {
        console.warn(`Custom field with id ${fieldData.id} not found`);
        return prev;
      }

      // Add new field với cấu trúc MarketingCustomFieldResponse + value
      const newField: SelectedCustomField = {
        ...customField,
        value: { value: '' }, // Default value
      };

      setHasChanges(true);
      return [...prev, newField];
    });
  }, [customFields]);

  // Handle update custom field value
  const handleUpdateCustomField = useCallback((fieldId: number, value: string | number | boolean) => {
    setAudienceCustomFields(prev => 
      prev.map(field => 
        field.id === fieldId 
          ? { ...field, value: { value } }
          : field
      )
    );
    setHasChanges(true);
  }, []);

  // Handle remove custom field
  const handleRemoveCustomField = useCallback((fieldId: number) => {
    setAudienceCustomFields(prev => prev.filter(field => field.id !== fieldId));
    setHasChanges(true);
  }, []);

  // Handle save custom fields
  const handleSaveCustomFields = useCallback(async () => {
    setIsSaving(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      console.log('Saving custom fields for audience:', audience.id, audienceCustomFields);
      
      // Reset changes flag
      setHasChanges(false);
      
    } catch (error) {
      console.error('Error saving custom fields:', error);
    } finally {
      setIsSaving(false);
    }
  }, [audienceCustomFields, audience.id]);

  return (
    <CollapsibleCard
      title={
        <Typography variant="h6" className="text-foreground">
          {t('marketing:audience.customFields')}
        </Typography>
      }
      defaultOpen={false}
    >
      <div className="space-y-4">
        {/* Generic Custom Field Selector */}
        <GenericCustomFieldSelector
          onFieldSelect={(fieldData) => {
            handleAddCustomField({
              id: fieldData.id,
              label: fieldData.label,
              dataType: fieldData.dataType,
              type: fieldData.type,
            });
          }}
          selectedFieldIds={audienceCustomFields.map(f => f.id)}
          placeholder={t('marketing:audience.customFields.searchPlaceholder', 'Nhập từ khóa để tìm trường tùy chỉnh...')}
          searchFunction={searchFunction}
          title={t('marketing:customField.title', 'Trường tùy chỉnh')}
          translationNamespace="marketing"
        />

        {/* Selected custom fields */}
        {audienceCustomFields.length > 0 && (
          <div className="space-y-3">
            {audienceCustomFields.map((field) => (
              <div key={field.id} className="p-3 bg-gray-50 dark:bg-gray-800 rounded-md">
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center space-x-2">
                    <Typography variant="caption" className="font-medium">
                      {field.displayName}
                    </Typography>
                    <span className="text-xs text-gray-500 bg-gray-200 dark:bg-gray-700 px-2 py-1 rounded">
                      {field.dataType || 'text'}
                    </span>
                  </div>
                  <button
                    type="button"
                    onClick={() => handleRemoveCustomField(field.id)}
                    className="text-red-500 hover:text-red-700 text-sm"
                  >
                    ✕
                  </button>
                </div>

                {/* Render field input dựa trên dataType */}
                <CustomFieldRenderer
                  field={{
                    id: field.id,
                    fieldId: field.id,
                    label: field.displayName,
                    component: field.dataType as string,
                    type: field.dataType as string,
                    required: false,
                    configJson: field.config as Record<string, unknown>,
                    value: field.value,
                  }}
                  value={(field.value['value'] as string) || ''}
                  onChange={value => handleUpdateCustomField(field.id, value as string)}
                  onRemove={() => handleRemoveCustomField(field.id)}
                />
              </div>
            ))}
          </div>
        )}

        {/* Empty state */}
        {audienceCustomFields.length === 0 && (
          <div className="text-center py-6 bg-gray-50 dark:bg-gray-800 rounded-lg">
            <Typography variant="body2" className="text-gray-500 dark:text-gray-400">
              {t('marketing:audience.customFields.noFields', 'Chưa có trường tùy chỉnh nào. Sử dụng ô tìm kiếm trên để thêm trường.')}
            </Typography>
          </div>
        )}

        {/* Save button */}
        {hasChanges && audienceCustomFields.length > 0 && (
          <div className="flex justify-end pt-4 border-t border-border">
            <IconCard
              icon="check"
              onClick={handleSaveCustomFields}
              disabled={isSaving}
              variant="primary"
              title={t('common:save')}
              size="md"
              isLoading={isSaving}
            />
          </div>
        )}
      </div>
    </CollapsibleCard>
  );
};

export default AudienceCustomFields;
