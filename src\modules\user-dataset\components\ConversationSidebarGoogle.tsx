import React from 'react';
import { <PERSON><PERSON>, <PERSON> } from '@/shared/components/common';
import { useTranslation } from 'react-i18next';
import { Plus, MessageSquare, Trash2, ChevronLeft, ChevronRight } from 'lucide-react';
import { ImportedConversation } from '../user-data-fine-tune/types/user-data-fine-tune.types';

interface ConversationSidebarGoogleProps {
  /**
   * Danh sách conversations
   */
  conversations: ImportedConversation[];

  /**
   * ID của conversation đang được chọn
   */
  selectedConversationId: string | null;

  /**
   * Callback khi chọn conversation
   */
  onSelectConversation: (conversationId: string) => void;

  /**
   * Callback khi xóa conversation
   */
  onDeleteConversation: (conversationId: string) => void;

  /**
   * Callback khi tạo chat mới
   */
  onNewChat: () => void;

  /**
   * Trạng thái mở/đóng sidebar
   */
  isOpen: boolean;

  /**
   * Callback toggle sidebar
   */
  onToggleSidebar: () => void;
}

/**
 * Conversation Sidebar cho Google DataForm
 * Không có chức năng import file JSON
 * Chỉ có: New Chat, Select Conversation, Delete Conversation
 */
const ConversationSidebarGoogle: React.FC<ConversationSidebarGoogleProps> = ({
  conversations,
  selectedConversationId,
  onSelectConversation,
  onDeleteConversation,
  onNewChat,
  isOpen,
  onToggleSidebar,
}) => {
  const { t } = useTranslation();

  // Truncate title if too long
  const truncateTitle = (title: string, maxLength: number = 30) => {
    return title.length > maxLength ? title.substring(0, maxLength) + '...' : title;
  };

  return (
    <div
      className={`h-full bg-white dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700 transition-all duration-300 ${isOpen ? 'w-80' : 'w-12'}`}
    >
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700">
        {isOpen && (
          <div className="flex items-center space-x-2">
            <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
            <h3 className="text-sm font-semibold text-gray-900 dark:text-white">
              {t('Google Conversations')}
            </h3>
          </div>
        )}

        <Button variant="ghost" size="sm" onClick={onToggleSidebar} className="p-2">
          {isOpen ? <ChevronLeft size={16} /> : <ChevronRight size={16} />}
        </Button>
      </div>

      {isOpen && (
        <>
          {/* New Chat Button */}
          <div className="p-4 border-b border-gray-200 dark:border-gray-700">
            <Button
              onClick={onNewChat}
              variant="primary"
              size="sm"
              className="w-full flex items-center justify-center bg-blue-600 hover:bg-blue-700"
            >
              <Plus size={16} className="mr-2" />
              {t('New Chat')}
            </Button>
          </div>

          {/* Conversations List */}
          <div className="flex-1 overflow-y-auto">
            {conversations.length === 0 ? (
              <div className="p-4 text-center">
                <div className="text-4xl mb-3">💬</div>
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  {t('Chưa có conversations')}
                </p>
                <p className="text-xs text-gray-400 dark:text-gray-500 mt-1">
                  {t('Tạo chat mới để bắt đầu')}
                </p>
              </div>
            ) : (
              <div className="p-2 space-y-1">
                {conversations.map(conversation => (
                  <Card
                    key={conversation.id}
                    className={`p-3 cursor-pointer transition-all duration-200 hover:shadow-md ${
                      selectedConversationId === conversation.id
                        ? 'bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-800'
                        : 'hover:bg-gray-50 dark:hover:bg-gray-700'
                    }`}
                    onClick={() => onSelectConversation(conversation.id)}
                  >
                    <div className="flex items-start justify-between">
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center space-x-2 mb-1">
                          <MessageSquare size={14} className="text-blue-500 flex-shrink-0" />
                          <h4 className="text-sm font-medium text-gray-900 dark:text-white truncate">
                            {truncateTitle(conversation.title)}
                          </h4>
                        </div>

                        <div className="flex items-center justify-between">
                          <span className="text-xs text-gray-500 dark:text-gray-400">
                            {conversation.messages.length} messages
                          </span>
                        </div>
                      </div>

                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={e => {
                          e.stopPropagation();
                          onDeleteConversation(conversation.id);
                        }}
                        className="p-1 ml-2 text-gray-400 hover:text-red-500 opacity-0 group-hover:opacity-100 transition-opacity"
                      >
                        <Trash2 size={14} />
                      </Button>
                    </div>

                    {/* Preview first message */}
                    {conversation.messages.length > 0 && (
                      <div className="mt-2 pt-2 border-t border-gray-100 dark:border-gray-600">
                        <p className="text-xs text-gray-500 dark:text-gray-400 line-clamp-2">
                          {conversation.messages[0]?.content.substring(0, 60)}
                          {conversation.messages?.[0]?.content &&
                            conversation.messages[0].content.length > 60 &&
                            '...'}
                        </p>
                      </div>
                    )}
                  </Card>
                ))}
              </div>
            )}
          </div>

          {/* Footer Info */}
          <div className="p-4 border-t border-gray-200 dark:border-gray-700">
            <div className="text-center">
              <p className="text-xs text-gray-500 dark:text-gray-400">
                {conversations.length} conversations
              </p>
              <div className="flex items-center justify-center mt-1">
                <div className="w-2 h-2 bg-blue-500 rounded-full mr-2"></div>
                <span className="text-xs text-blue-600 dark:text-blue-400 font-medium">
                  Google Provider
                </span>
              </div>
            </div>
          </div>
        </>
      )}
    </div>
  );
};

export default ConversationSidebarGoogle;
