import React from 'react';
import { Card } from '@/shared/components/common';
import { useTranslation } from 'react-i18next';

interface ProviderSelectionCardsProps {
  onSelectOpenAI: () => void;
  onSelectGoogle: () => void;
}

const ProviderSelectionCards: React.FC<ProviderSelectionCardsProps> = ({
  onSelectOpenAI,
  onSelectGoogle,
}) => {
  const { t } = useTranslation();
  return (
    <div className="grid grid-cols-2 gap-4 mt-4">
      <Card
        onClick={onSelectOpenAI}
        className="cursor-pointer hover:shadow-lg transition-shadow duration-300"
        variant="elevated"
      >
        <div className="p-4 text-center">
          <h3 className="text-lg font-semibold mb-2">OpenAI Provider</h3>
          <p className="text-gray-600">
            {t(
              'user-dataset:providerSelection.createDatasetUsingOpenAI',
              'Create dataset using OpenAI'
            )}
          </p>
        </div>
      </Card>

      <Card
        onClick={onSelectGoogle}
        className="cursor-pointer hover:shadow-lg transition-shadow duration-300"
        variant="elevated"
      >
        <div className="p-4 text-center">
          <h3 className="text-lg font-semibold mb-2">Google Provider</h3>
          <p className="text-gray-600">
            {' '}
            {t(
              'user-dataset:providerSelection.createDatasetUsingGoogle',
              'Create dataset using Google'
            )}
          </p>
        </div>
      </Card>
    </div>
  );
};

export default ProviderSelectionCards;
