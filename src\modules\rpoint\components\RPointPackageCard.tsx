/**
 * Component hiển thị một gói R-Point
 */
import React from 'react';
import { Card, Typography, Button } from '@/shared/components/common';
import { RPointPackage } from '../types/rpoint.types';
import { formatVND, formatRPoint } from '../utils/rpoint.utils';

// Import hình ảnh R-Point
import rpointImage from '@/shared/assets/images/rpoint.png';

interface RPointPackageCardProps {
  /**
   * Thông tin gói R-Point
   */
  package: RPointPackage;

  /**
   * Callback khi chọn gói
   */
  onSelect: (pkg: RPointPackage) => void;

  /**
   * Class bổ sung
   */
  className?: string;
}

/**
 * Component hiển thị thông tin một gói R-Point
 */
const RPointPackageCard: React.FC<RPointPackageCardProps> = ({
  package: pkg,
  onSelect,
  className = '',
}) => {
  return (
    <Card
      variant={pkg.isPopular ? 'elevated' : 'default'}
      {...(pkg.isPopular && { colorBorder: 'primary' })}
      className={`h-full flex flex-col ${className}`}
      hoverable
    >
      {pkg.isPopular && (
        <div className="absolute top-0 right-0 bg-primary text-white px-3 py-1 text-xs font-medium rounded-bl-lg rounded-tr-lg">
          Phổ biến
        </div>
      )}

      <div className="p-6 flex-grow">
        <div className="flex items-center mb-4">
          <div className="flex items-center">
            <img src={rpointImage} alt="R-Point" className="w-8 h-8 mr-2" />
            <Typography variant="h4" className="font-bold">
              {formatRPoint(pkg.points)}
            </Typography>
          </div>
        </div>

      

        {pkg.description && (
          <Typography variant="body1" className="mb-4 text-muted">
            {pkg.description}
          </Typography>
        )}

        <div className="flex items-center mb-2">
          <Typography variant="h5" className="font-bold text-primary">
            {formatVND(pkg.price)}
          </Typography>
        </div>
      </div>

      <div className="p-4 border-t border-border">
        <Button variant="primary" fullWidth onClick={() => onSelect(pkg)}>
          Chọn gói
        </Button>
      </div>
    </Card>
  );
};

export default RPointPackageCard;
