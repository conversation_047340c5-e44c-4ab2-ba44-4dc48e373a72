import React, { useState } from 'react';
import { Button, Card, Icon, Input, Select, Badge } from '@/shared/components/common';
import { SortDirection, TypeAgentSortBy } from '../../types';
import { TypeAgentFilterData } from './TypeAgentFilter';

export interface AdvancedTypeAgentFilterProps {
  /**
   * Giá trị filter hiện tại
   */
  filters: TypeAgentFilterData;

  /**
   * Callback khi thay đổi filter
   */
  onFilterChange?: (filters: TypeAgentFilterData) => void;

  /**
   * Callback khi reset filter
   */
  onReset?: () => void;

  /**
   * Hiển thị dạng collapsed
   */
  collapsed?: boolean;

  /**
   * Callback khi toggle collapsed
   */
  onToggleCollapsed?: (collapsed: boolean) => void;
}

/**
 * Component filter nâng cao cho danh sách Type Agent
 */
const AdvancedTypeAgentFilter: React.FC<AdvancedTypeAgentFilterProps> = ({
  filters,
  onFilterChange,
  onReset,
  collapsed = false,
  onToggleCollapsed
}) => {
  // Local state cho form
  const [localFilters, setLocalFilters] = useState<TypeAgentFilterData>(filters);

  // Options cho select
  const agentTypeOptions = [
    { value: '', label: 'Tất cả loại' },
    { value: 'true', label: 'System Agents' },
    { value: 'false', label: 'User Agents' }
  ];

  const sortByOptions = [
    { value: TypeAgentSortBy.NAME, label: 'Tên' },
    { value: TypeAgentSortBy.CREATED_AT, label: 'Ngày tạo' }
  ];

  const sortDirectionOptions = [
    { value: SortDirection.ASC, label: 'Tăng dần' },
    { value: SortDirection.DESC, label: 'Giảm dần' }
  ];

  // Xử lý thay đổi input
  const handleInputChange = (field: keyof TypeAgentFilterData, value: string | boolean | TypeAgentSortBy | SortDirection | undefined) => {
    const newFilters = {
      ...localFilters,
      [field]: value
    };
    setLocalFilters(newFilters);

    // Gọi callback ngay lập tức cho search
    if (field === 'search') {
      onFilterChange?.(newFilters);
    }
  };

  // Xử lý apply filter
  const handleApplyFilter = () => {
    onFilterChange?.(localFilters);
  };

  // Xử lý reset filter
  const handleReset = () => {
    const resetFilters: TypeAgentFilterData = {
      search: '',
      sortBy: TypeAgentSortBy.CREATED_AT,
      sortDirection: SortDirection.DESC
    };
    setLocalFilters(resetFilters);
    onFilterChange?.(resetFilters);
    onReset?.();
  };

  // Kiểm tra có filter nào được áp dụng không
  const hasActiveFilters = localFilters.search ||
                          localFilters.isSystem !== undefined ||
                          localFilters.sortBy !== TypeAgentSortBy.CREATED_AT ||
                          localFilters.sortDirection !== SortDirection.DESC;

  // Đếm số filter active
  const activeFilterCount = [
    localFilters.search,
    localFilters.isSystem !== undefined,
    localFilters.sortBy !== TypeAgentSortBy.CREATED_AT,
    localFilters.sortDirection !== SortDirection.DESC
  ].filter(Boolean).length;

  return (
    <Card className="overflow-hidden">
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center gap-2">
          <h3 className="text-lg font-semibold">Bộ lọc nâng cao</h3>
          {activeFilterCount > 0 && (
            <Badge variant="primary" size="sm">
              {activeFilterCount}
            </Badge>
          )}
        </div>

        <div className="flex items-center gap-2">
          {hasActiveFilters && (
            <Button
              variant="ghost"
              size="sm"
              onClick={handleReset}
              leftIcon={<Icon name="refresh-cw" size="sm" />}
            >
              Đặt lại
            </Button>
          )}

          <Button
            variant="ghost"
            size="sm"
            onClick={() => onToggleCollapsed?.(!collapsed)}
            leftIcon={<Icon name={collapsed ? "chevron-down" : "chevron-up"} size="sm" />}
          >
            {collapsed ? 'Mở rộng' : 'Thu gọn'}
          </Button>
        </div>
      </div>

      {/* Content */}
      {!collapsed && (
        <div className="p-4">
          {/* Quick Search */}
          <div className="mb-4">
            <label className="block text-sm font-medium mb-2">Tìm kiếm nhanh</label>
            <Input
              placeholder="Nhập tên loại agent..."
              value={localFilters.search || ''}
              onChange={(e) => handleInputChange('search', e.target.value)}
              leftIcon={<Icon name="search" size="sm" />}
              rightIcon={
                localFilters.search && (
                  <button
                    onClick={() => handleInputChange('search', '')}
                    className="text-gray-400 hover:text-gray-600"
                  >
                    <Icon name="x" size="sm" />
                  </button>
                )
              }
            />
          </div>

          {/* Filter Grid */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
            {/* Agent Type */}
            <div>
              <label className="block text-sm font-medium mb-2">Loại Agent</label>
              <Select
                options={agentTypeOptions}
                value={localFilters.isSystem?.toString() || ''}
                onChange={(value) => handleInputChange('isSystem', value === '' ? undefined : value === 'true')}
                placeholder="Chọn loại agent"
              />
            </div>

            {/* Sort By */}
            <div>
              <label className="block text-sm font-medium mb-2">Sắp xếp theo</label>
              <Select
                options={sortByOptions}
                value={localFilters.sortBy || TypeAgentSortBy.CREATED_AT}
                onChange={(value) => handleInputChange('sortBy', value as TypeAgentSortBy)}
              />
            </div>

            {/* Sort Direction */}
            <div>
              <label className="block text-sm font-medium mb-2">Thứ tự</label>
              <Select
                options={sortDirectionOptions}
                value={localFilters.sortDirection || SortDirection.DESC}
                onChange={(value) => handleInputChange('sortDirection', value as SortDirection)}
              />
            </div>
          </div>

          {/* Quick Filter Buttons */}
          <div className="mb-4">
            <label className="block text-sm font-medium mb-2">Bộ lọc nhanh</label>
            <div className="flex flex-wrap gap-2">
              <Button
                variant={localFilters.isSystem === true ? "primary" : "outline"}
                size="sm"
                onClick={() => handleInputChange('isSystem', true)}
              >
                System Agents
              </Button>
              <Button
                variant={localFilters.isSystem === false ? "primary" : "outline"}
                size="sm"
                onClick={() => handleInputChange('isSystem', false)}
              >
                User Agents
              </Button>
              <Button
                variant={localFilters.sortBy === TypeAgentSortBy.NAME ? "primary" : "outline"}
                size="sm"
                onClick={() => handleInputChange('sortBy', TypeAgentSortBy.NAME)}
              >
                Sắp xếp theo tên
              </Button>
              <Button
                variant={localFilters.sortBy === TypeAgentSortBy.CREATED_AT ? "primary" : "outline"}
                size="sm"
                onClick={() => handleInputChange('sortBy', TypeAgentSortBy.CREATED_AT)}
              >
                Sắp xếp theo ngày
              </Button>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex justify-end gap-2 pt-4 border-t border-gray-200 dark:border-gray-700">
            <Button
              variant="outline"
              onClick={handleReset}
              disabled={!hasActiveFilters}
            >
              Đặt lại tất cả
            </Button>
            <Button
              variant="primary"
              onClick={handleApplyFilter}
            >
              Áp dụng bộ lọc
            </Button>
          </div>
        </div>
      )}
    </Card>
  );
};

export default AdvancedTypeAgentFilter;
