import React, { useState, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { Table, TableColumn } from '@/shared/components/common/Table';
import { Tooltip, IconCard, SearchBar, Checkbox } from '@/shared/components/common';
import { ModernMenu } from '@/shared/components/common';

// Interface cho cài đặt hiển thị cột
interface ColumnVisibility {
  id: string;
  label: string;
  visible: boolean;
}

import { usePurchaseHistory } from '../../hooks/usePurchaseHistory';
import {
  PurchaseHistoryQueryParams,
  PaginatedPurchaseHistory,
} from '../../types/purchase-history.types';

// Import hình ảnh R-Point
import rpointImage from '@/shared/assets/images/rpoint.png';

/**
 * Component hiển thị bảng lịch sử mua hàng
 */
const PurchaseHistoryTable: React.FC = () => {
  const { t } = useTranslation('profile');
  const [searchText, setSearchText] = useState('');

  const [queryParams, setQueryParams] = useState<PurchaseHistoryQueryParams>({
    page: 1,
    pageSize: 5,
  });
  const [showSearch, setShowSearch] = useState(false);
  const [showColumnMenu, setShowColumnMenu] = useState(false);
  const [showFilterMenu, setShowFilterMenu] = useState(false);

  const [columnSettings, setColumnSettings] = useState<ColumnVisibility[]>([
    { id: 'all', label: t('common.selectAll', 'Chọn tất cả'), visible: true },
    { id: 'id', label: 'ID', visible: true },
    { id: 'purchaseDate', label: t('purchaseHistory.purchaseDate', 'Ngày mua'), visible: true },
    { id: 'amount', label: t('purchaseHistory.amount', 'Thanh toán'), visible: true },
    { id: 'points', label: t('purchaseHistory.points', 'Số point'), visible: true },
  ]);

  // Lấy dữ liệu lịch sử mua hàng
  const { data, isLoading } = usePurchaseHistory(queryParams);

  // Đảm bảo dữ liệu có cấu trúc đúng
  const purchaseData: PaginatedPurchaseHistory = {
    items: (data as PaginatedPurchaseHistory)?.items || [],
    total: (data as PaginatedPurchaseHistory)?.total || 0,
    page: (data as PaginatedPurchaseHistory)?.page || queryParams.page || 1,
    pageSize: (data as PaginatedPurchaseHistory)?.pageSize || queryParams.pageSize || 5,
  };

  // Không cần các hàm handlePageChange và handlePageSizeChange riêng
  // vì đã xử lý trong pagination của Table

  // Xử lý khi thay đổi từ khóa tìm kiếm
  const handleSearchChange = (value: string) => {
    setSearchText(value);
    setQueryParams(prev => ({ ...prev, page: 1, search: value }));
  };

  // Xử lý khi thay đổi sắp xếp

  // Không cần danh sách tùy chọn cột vì đã sử dụng columnSettings

  // Xử lý thay đổi hiển thị cột

  // Định nghĩa các cột cho bảng
  const allColumns: TableColumn[] = useMemo(
    () => [
      {
        title: 'ID',
        dataIndex: 'id',
        key: 'id',
        width: '10%',
        sorter: true,
      },
      {
        title: t('purchaseHistory.purchaseDate'),
        dataIndex: 'purchaseDate',
        key: 'purchaseDate',
        width: '30%',
        sorter: true,
      },
      {
        title: t('purchaseHistory.amount'),
        dataIndex: 'amount',
        key: 'amount',
        width: '30%',
        sorter: true,
        render: (value: unknown) => (
          <div className="flex items-center">
            {(value as number).toLocaleString()}
            <img src={rpointImage} alt="R-Point" className="ml-2 w-4 h-4" />
          </div>
        ),
      },
      {
        title: t('purchaseHistory.points'),
        dataIndex: 'points',
        key: 'points',
        width: '30%',
        sorter: true,
        render: (value: unknown) => (
          <div className="flex items-center">
            {(value as number).toLocaleString()}
            <img src={rpointImage} alt="R-Point" className="ml-2 w-4 h-4" />
          </div>
        ),
      },
    ],
    [t]
  );

  // Lọc các cột theo columnSettings
  const columns = useMemo(() => {
    const visibleColumnIds = columnSettings
      .filter(col => col.visible && col.id !== 'all')
      .map(col => col.id);
    return allColumns.filter(column => visibleColumnIds.includes(column.key));
  }, [allColumns, columnSettings]);

  // Xử lý khi click vào nút tìm kiếm
  const handleSearchClick = () => {
    setShowSearch(!showSearch);
    if (showSearch && searchText) {
      setSearchText('');
      setQueryParams(prev => ({ ...prev, page: 1, search: '' }));
    }
  };

  // Xử lý khi click vào nút lọc
  const handleFilterClick = () => {
    setShowFilterMenu(!showFilterMenu);
  };

  return (
    <div className="space-y-4">
      {/* Bộ lọc và tìm kiếm */}
      <div className="flex flex-col sm:flex-row items-start sm:items-center space-y-3 sm:space-y-0 sm:space-x-3 mb-4">
        <div className="flex items-center space-x-2">
          <Tooltip content={t('common.search')} position="bottom">
            <IconCard
              icon="search"
              variant={showSearch ? 'primary' : 'default'}
              onClick={handleSearchClick}
              active={showSearch}
            />
          </Tooltip>

          <div className="relative">
            <Tooltip content={t('common.filter', 'Lọc')} position="bottom">
              <IconCard
                icon="filter"
                variant={showFilterMenu ? 'primary' : 'default'}
                onClick={handleFilterClick}
                active={showFilterMenu}
              />

              {showFilterMenu && (
                <ModernMenu
                  isOpen={showFilterMenu}
                  onClose={() => setShowFilterMenu(false)}
                  placement="bottom"
                  width="180px"
                  items={[
                    {
                      id: 'all',
                      label: t('common.all', 'Tất cả'),
                      icon: 'list',
                      onClick: () => {
                        // Xử lý khi chọn tất cả
                        setShowFilterMenu(false);
                      },
                    },
                    {
                      id: 'today',
                      label: t('purchaseHistory.today', 'Hôm nay'),
                      icon: 'calendar-day',
                      onClick: () => {
                        // Xử lý khi chọn hôm nay
                        setShowFilterMenu(false);
                      },
                    },
                    {
                      id: 'thisWeek',
                      label: t('purchaseHistory.thisWeek', 'Tuần này'),
                      icon: 'calendar-week',
                      onClick: () => {
                        // Xử lý khi chọn tuần này
                        setShowFilterMenu(false);
                      },
                    },
                    {
                      id: 'thisMonth',
                      label: t('purchaseHistory.thisMonth', 'Tháng này'),
                      icon: 'calendar-alt',
                      onClick: () => {
                        // Xử lý khi chọn tháng này
                        setShowFilterMenu(false);
                      },
                    },
                  ]}
                />
              )}
            </Tooltip>
          </div>

          <div className="relative">
            <Tooltip content={t('common.columns', 'Cột hiển thị')} position="right">
              <IconCard
                icon="layers"
                variant="default"
                onClick={() => setShowColumnMenu(!showColumnMenu)}
              />

              {showColumnMenu && (
                <ModernMenu
                  isOpen={showColumnMenu}
                  onClose={() => setShowColumnMenu(false)}
                  placement="bottom"
                  width="220px"
                  items={[
                    {
                      id: 'header',
                      label: (
                        <div className="font-medium text-sm mb-1">
                          {t('common.columns', 'Cột hiển thị')}
                        </div>
                      ),
                      divider: true,
                    },
                    {
                      id: 'all',
                      label: (
                        <div className="flex items-center">
                          <Checkbox
                            id="column-all"
                            {...(columnSettings.find(col => col.id === 'all')?.visible !== undefined && {
                              checked: columnSettings.find(col => col.id === 'all')?.visible
                            })}
                            onChange={checked => {
                              const updatedColumns = columnSettings.map(col => ({
                                ...col,
                                visible: checked,
                              }));
                              setColumnSettings(updatedColumns);
                            }}
                            variant="filled"
                            size="sm"
                            label={
                              <span className="ml-1 text-sm">
                                {t('common.selectAll', 'Chọn tất cả')}
                              </span>
                            }
                          />
                        </div>
                      ),
                    },
                    ...columnSettings
                      .filter(column => column.id !== 'all')
                      .map(column => ({
                        id: column.id,
                        label: (
                          <div className="flex items-center">
                            <Checkbox
                              id={`column-${column.id}`}
                              checked={column.visible}
                              onChange={checked => {
                                const updatedColumns = [...columnSettings];
                                const columnToUpdate = updatedColumns.find(
                                  col => col.id === column.id
                                );
                                if (columnToUpdate) {
                                  columnToUpdate.visible = checked;
                                }

                                // Cập nhật trạng thái "Chọn tất cả"
                                const allColumn = updatedColumns.find(col => col.id === 'all');
                                if (allColumn) {
                                  const otherColumns = updatedColumns.filter(
                                    col => col.id !== 'all'
                                  );
                                  allColumn.visible = otherColumns.every(col => col.visible);
                                }

                                setColumnSettings(updatedColumns);
                              }}
                              variant="filled"
                              size="sm"
                              label={<span className="ml-1 text-sm">{column.label}</span>}
                            />
                          </div>
                        ),
                      })),
                  ]}
                />
              )}
            </Tooltip>
          </div>
        </div>

        {/* Chỉ hiển thị SearchBar khi showSearch = true */}
        <div className="w-full sm:w-auto">
          <SearchBar
            visible={showSearch}
            value={searchText}
            onChange={handleSearchChange}
            onToggle={handleSearchClick}
            maxWidth="100%"
            variant="flat"
            autoFocus={true}
            showSearchIcon={false}
            className="w-full"
            placeholder={t('purchaseHistory.searchPlaceholder')}
          />
        </div>
      </div>

      {/* Bảng dữ liệu với phân trang tích hợp */}
      <Table
        data={purchaseData.items}
        columns={columns}
        loading={isLoading}
        sortable={true}
        pagination={{
          total: purchaseData.total,
          current: queryParams.page || 1,
          pageSize: queryParams.pageSize || 5,
          onChange: (page, pageSize) => {
            if (pageSize !== queryParams.pageSize) {
              setQueryParams(prev => ({ ...prev, page: 1, pageSize }));
            } else {
              setQueryParams(prev => ({ ...prev, page }));
            }
          },
          pageSizeOptions: [5, 10, 20, 50],
          showSizeChanger: true,
          showFirstLastButtons: true,
        }}
        bordered
        hoverable
      />
    </div>
  );
};

export default PurchaseHistoryTable;
